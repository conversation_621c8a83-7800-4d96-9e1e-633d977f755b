// التقارير والإحصائيات
function getReportsHTML() {
    return `
        <div class="reports-container">
            <div class="page-header">
                <h2 class="page-title">التقارير والإحصائيات</h2>
                <div class="page-actions">
                    <button class="btn btn-success" onclick="exportAllReports()">
                        <i class="fas fa-download"></i>
                        تصدير جميع التقارير
                    </button>
                </div>
            </div>

            <!-- فلاتر التقارير -->
            <div class="reports-filters">
                <div class="filter-group">
                    <label>الفترة الزمنية:</label>
                    <select id="reportPeriod" onchange="updateReports()">
                        <option value="today">اليوم</option>
                        <option value="week">هذا الأسبوع</option>
                        <option value="month" selected>هذا الشهر</option>
                        <option value="quarter">هذا الربع</option>
                        <option value="year">هذا العام</option>
                        <option value="custom">فترة مخصصة</option>
                    </select>
                </div>
                <div class="filter-group custom-period hidden" id="customPeriodGroup">
                    <input type="date" id="reportDateFrom">
                    <span>إلى</span>
                    <input type="date" id="reportDateTo">
                </div>
                <div class="filter-group">
                    <button class="btn btn-primary" onclick="updateReports()">
                        <i class="fas fa-sync"></i>
                        تحديث التقارير
                    </button>
                </div>
            </div>

            <!-- ملخص الأداء -->
            <div class="performance-summary">
                <h3>ملخص الأداء</h3>
                <div class="summary-cards">
                    <div class="summary-card revenue">
                        <div class="card-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="card-content">
                            <h4 id="totalRevenue">٠.٠٠ ريال</h4>
                            <p>إجمالي الإيرادات</p>
                            <span class="trend" id="revenueTrend">+٠%</span>
                        </div>
                    </div>
                    
                    <div class="summary-card profit">
                        <div class="card-icon">
                            <i class="fas fa-coins"></i>
                        </div>
                        <div class="card-content">
                            <h4 id="totalProfit">٠.٠٠ ريال</h4>
                            <p>إجمالي الأرباح</p>
                            <span class="trend" id="profitTrend">+٠%</span>
                        </div>
                    </div>
                    
                    <div class="summary-card expenses">
                        <div class="card-icon">
                            <i class="fas fa-shopping-cart"></i>
                        </div>
                        <div class="card-content">
                            <h4 id="totalExpenses">٠.٠٠ ريال</h4>
                            <p>إجمالي المصروفات</p>
                            <span class="trend" id="expensesTrend">+٠%</span>
                        </div>
                    </div>
                    
                    <div class="summary-card margin">
                        <div class="card-icon">
                            <i class="fas fa-percentage"></i>
                        </div>
                        <div class="card-content">
                            <h4 id="profitMargin">٠%</h4>
                            <p>هامش الربح</p>
                            <span class="trend" id="marginTrend">+٠%</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- تقارير مفصلة -->
            <div class="detailed-reports">
                <div class="reports-tabs">
                    <button class="tab-btn active" onclick="showReportTab('sales')">
                        <i class="fas fa-chart-bar"></i>
                        تقرير المبيعات
                    </button>
                    <button class="tab-btn" onclick="showReportTab('inventory')">
                        <i class="fas fa-boxes"></i>
                        تقرير المخزون
                    </button>
                    <button class="tab-btn" onclick="showReportTab('customers')">
                        <i class="fas fa-users"></i>
                        تقرير العملاء
                    </button>
                    <button class="tab-btn" onclick="showReportTab('financial')">
                        <i class="fas fa-calculator"></i>
                        التقرير المالي
                    </button>
                </div>

                <!-- تقرير المبيعات -->
                <div id="salesReport" class="report-tab active">
                    <div class="report-header">
                        <h4>تقرير المبيعات</h4>
                        <button class="btn btn-sm btn-success" onclick="exportSalesReport()">
                            <i class="fas fa-download"></i>
                            تصدير
                        </button>
                    </div>
                    
                    <div class="report-stats">
                        <div class="stat-item">
                            <span class="label">عدد الفواتير:</span>
                            <span class="value" id="salesInvoicesCount">٠</span>
                        </div>
                        <div class="stat-item">
                            <span class="label">متوسط قيمة الفاتورة:</span>
                            <span class="value" id="averageInvoiceValue">٠.٠٠ ريال</span>
                        </div>
                        <div class="stat-item">
                            <span class="label">أفضل يوم مبيعات:</span>
                            <span class="value" id="bestSalesDay">-</span>
                        </div>
                    </div>
                    
                    <div class="report-chart">
                        <canvas id="salesChart" width="400" height="200"></canvas>
                    </div>
                    
                    <div class="top-products">
                        <h5>أفضل المنتجات مبيعاً</h5>
                        <div id="topProductsList" class="products-list">
                            <!-- سيتم ملء القائمة هنا -->
                        </div>
                    </div>
                </div>

                <!-- تقرير المخزون -->
                <div id="inventoryReport" class="report-tab hidden">
                    <div class="report-header">
                        <h4>تقرير المخزون</h4>
                        <button class="btn btn-sm btn-success" onclick="exportInventoryReport()">
                            <i class="fas fa-download"></i>
                            تصدير
                        </button>
                    </div>
                    
                    <div class="report-stats">
                        <div class="stat-item">
                            <span class="label">إجمالي المنتجات:</span>
                            <span class="value" id="totalProductsReport">٠</span>
                        </div>
                        <div class="stat-item">
                            <span class="label">قيمة المخزون:</span>
                            <span class="value" id="inventoryValueReport">٠.٠٠ ريال</span>
                        </div>
                        <div class="stat-item">
                            <span class="label">منتجات منخفضة المخزون:</span>
                            <span class="value" id="lowStockProductsReport">٠</span>
                        </div>
                    </div>
                    
                    <div class="inventory-analysis">
                        <div class="analysis-section">
                            <h5>تحليل المخزون</h5>
                            <div id="inventoryAnalysis" class="analysis-content">
                                <!-- سيتم ملء التحليل هنا -->
                            </div>
                        </div>
                        
                        <div class="low-stock-section">
                            <h5>المنتجات منخفضة المخزون</h5>
                            <div id="lowStockProducts" class="low-stock-list">
                                <!-- سيتم ملء القائمة هنا -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- تقرير العملاء -->
                <div id="customersReport" class="report-tab hidden">
                    <div class="report-header">
                        <h4>تقرير العملاء</h4>
                        <button class="btn btn-sm btn-success" onclick="exportCustomersReport()">
                            <i class="fas fa-download"></i>
                            تصدير
                        </button>
                    </div>
                    
                    <div class="report-stats">
                        <div class="stat-item">
                            <span class="label">إجمالي العملاء:</span>
                            <span class="value" id="totalCustomersReport">٠</span>
                        </div>
                        <div class="stat-item">
                            <span class="label">عملاء نشطون:</span>
                            <span class="value" id="activeCustomersReport">٠</span>
                        </div>
                        <div class="stat-item">
                            <span class="label">إجمالي الديون:</span>
                            <span class="value" id="totalDebtsReport">٠.٠٠ ريال</span>
                        </div>
                    </div>
                    
                    <div class="customers-analysis">
                        <div class="top-customers">
                            <h5>أفضل العملاء</h5>
                            <div id="topCustomersList" class="customers-list">
                                <!-- سيتم ملء القائمة هنا -->
                            </div>
                        </div>
                        
                        <div class="debtor-customers">
                            <h5>العملاء المدينون</h5>
                            <div id="debtorCustomersList" class="debtors-list">
                                <!-- سيتم ملء القائمة هنا -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- التقرير المالي -->
                <div id="financialReport" class="report-tab hidden">
                    <div class="report-header">
                        <h4>التقرير المالي</h4>
                        <button class="btn btn-sm btn-success" onclick="exportFinancialReport()">
                            <i class="fas fa-download"></i>
                            تصدير
                        </button>
                    </div>
                    
                    <div class="financial-summary">
                        <div class="financial-section">
                            <h5>الإيرادات والمصروفات</h5>
                            <div class="financial-table">
                                <div class="financial-row">
                                    <span class="label">إجمالي المبيعات:</span>
                                    <span class="value positive" id="financialSales">٠.٠٠ ريال</span>
                                </div>
                                <div class="financial-row">
                                    <span class="label">تكلفة البضائع المباعة:</span>
                                    <span class="value negative" id="financialCOGS">٠.٠٠ ريال</span>
                                </div>
                                <div class="financial-row total">
                                    <span class="label">إجمالي الربح:</span>
                                    <span class="value" id="financialGrossProfit">٠.٠٠ ريال</span>
                                </div>
                                <div class="financial-row">
                                    <span class="label">المصروفات التشغيلية:</span>
                                    <span class="value negative" id="financialExpenses">٠.٠٠ ريال</span>
                                </div>
                                <div class="financial-row total">
                                    <span class="label">صافي الربح:</span>
                                    <span class="value" id="financialNetProfit">٠.٠٠ ريال</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="cash-flow-section">
                            <h5>التدفق النقدي</h5>
                            <div id="cashFlowAnalysis" class="cash-flow-content">
                                <!-- سيتم ملء تحليل التدفق النقدي هنا -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
}

// متغيرات التقارير
let currentReportPeriod = 'month';
let reportDateRange = {};

// تهيئة صفحة التقارير
function initializeReports() {
    setupReportPeriodListener();
    updateReportDateRange();
    updateReports();
}

// إعداد مستمع تغيير الفترة الزمنية
function setupReportPeriodListener() {
    const periodSelect = document.getElementById('reportPeriod');
    const customPeriodGroup = document.getElementById('customPeriodGroup');
    
    periodSelect.addEventListener('change', function() {
        if (this.value === 'custom') {
            customPeriodGroup.classList.remove('hidden');
        } else {
            customPeriodGroup.classList.add('hidden');
        }
        currentReportPeriod = this.value;
        updateReportDateRange();
    });
}

// تحديث نطاق التاريخ للتقرير
function updateReportDateRange() {
    const now = new Date();

    switch (currentReportPeriod) {
        case 'today':
            reportDateRange = {
                start: new Date(now.getFullYear(), now.getMonth(), now.getDate()),
                end: new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59)
            };
            break;
        case 'week':
            const weekStart = new Date(now);
            weekStart.setDate(now.getDate() - now.getDay());
            reportDateRange = {
                start: weekStart,
                end: now
            };
            break;
        case 'month':
            reportDateRange = {
                start: new Date(now.getFullYear(), now.getMonth(), 1),
                end: now
            };
            break;
        case 'quarter':
            const quarterStart = new Date(now.getFullYear(), Math.floor(now.getMonth() / 3) * 3, 1);
            reportDateRange = {
                start: quarterStart,
                end: now
            };
            break;
        case 'year':
            reportDateRange = {
                start: new Date(now.getFullYear(), 0, 1),
                end: now
            };
            break;
        case 'custom':
            const dateFrom = document.getElementById('reportDateFrom').value;
            const dateTo = document.getElementById('reportDateTo').value;
            if (dateFrom && dateTo) {
                reportDateRange = {
                    start: new Date(dateFrom),
                    end: new Date(dateTo + 'T23:59:59')
                };
            }
            break;
    }
}

// تحديث جميع التقارير
function updateReports() {
    updateReportDateRange();
    updatePerformanceSummary();
    updateSalesReport();
    updateInventoryReport();
    updateCustomersReport();
    updateFinancialReport();
}

// تحديث ملخص الأداء
function updatePerformanceSummary() {
    const sales = getFilteredSales();
    const purchases = getFilteredPurchases();

    const totalRevenue = sales.reduce((sum, sale) => sum + sale.total, 0);
    const totalExpenses = purchases.reduce((sum, purchase) => sum + purchase.total, 0);
    const totalProfit = totalRevenue - totalExpenses;
    const profitMargin = totalRevenue > 0 ? (totalProfit / totalRevenue) * 100 : 0;

    document.getElementById('totalRevenue').textContent = formatCurrency(totalRevenue);
    document.getElementById('totalProfit').textContent = formatCurrency(totalProfit);
    document.getElementById('totalExpenses').textContent = formatCurrency(totalExpenses);
    document.getElementById('profitMargin').textContent = formatArabicNumber(profitMargin.toFixed(1)) + '%';

    // يمكن إضافة حساب الاتجاهات هنا
    document.getElementById('revenueTrend').textContent = '+٠%';
    document.getElementById('profitTrend').textContent = '+٠%';
    document.getElementById('expensesTrend').textContent = '+٠%';
    document.getElementById('marginTrend').textContent = '+٠%';
}

// الحصول على المبيعات المفلترة
function getFilteredSales() {
    const sales = db.getData('sales');
    return sales.filter(sale => {
        const saleDate = new Date(sale.createdAt);
        return saleDate >= reportDateRange.start && saleDate <= reportDateRange.end;
    });
}

// الحصول على المشتريات المفلترة
function getFilteredPurchases() {
    const purchases = db.getData('purchases');
    return purchases.filter(purchase => {
        const purchaseDate = new Date(purchase.createdAt);
        return purchaseDate >= reportDateRange.start && purchaseDate <= reportDateRange.end;
    });
}

// تحديث تقرير المبيعات
function updateSalesReport() {
    const sales = getFilteredSales();

    const invoicesCount = sales.length;
    const averageInvoiceValue = invoicesCount > 0 ? sales.reduce((sum, sale) => sum + sale.total, 0) / invoicesCount : 0;

    document.getElementById('salesInvoicesCount').textContent = formatArabicNumber(invoicesCount);
    document.getElementById('averageInvoiceValue').textContent = formatCurrency(averageInvoiceValue);

    // أفضل يوم مبيعات
    const salesByDay = {};
    sales.forEach(sale => {
        const day = new Date(sale.createdAt).toDateString();
        salesByDay[day] = (salesByDay[day] || 0) + sale.total;
    });

    const bestDay = Object.keys(salesByDay).reduce((a, b) => salesByDay[a] > salesByDay[b] ? a : b, '');
    document.getElementById('bestSalesDay').textContent = bestDay ? formatDate(bestDay) : '-';

    // أفضل المنتجات مبيعاً
    updateTopProducts(sales);
}

// تحديث أفضل المنتجات مبيعاً
function updateTopProducts(sales) {
    const productSales = {};

    sales.forEach(sale => {
        sale.items.forEach(item => {
            if (productSales[item.name]) {
                productSales[item.name].quantity += item.quantity;
                productSales[item.name].total += item.total;
            } else {
                productSales[item.name] = {
                    name: item.name,
                    quantity: item.quantity,
                    total: item.total
                };
            }
        });
    });

    const topProducts = Object.values(productSales)
        .sort((a, b) => b.total - a.total)
        .slice(0, 5);

    const topProductsList = document.getElementById('topProductsList');
    if (topProducts.length === 0) {
        topProductsList.innerHTML = '<div class="no-data">لا توجد مبيعات في هذه الفترة</div>';
    } else {
        topProductsList.innerHTML = topProducts.map(product => `
            <div class="product-item">
                <div class="product-info">
                    <span class="product-name">${product.name}</span>
                    <span class="product-quantity">الكمية: ${formatArabicNumber(product.quantity)}</span>
                </div>
                <div class="product-total">${formatCurrency(product.total)}</div>
            </div>
        `).join('');
    }
}

// تحديث تقرير المخزون
function updateInventoryReport() {
    const products = db.getData('products');

    const totalProducts = products.length;
    const inventoryValue = products.reduce((sum, product) => sum + (product.quantity * (product.cost || product.price)), 0);
    const lowStockProducts = products.filter(product => product.quantity <= product.minStock);

    document.getElementById('totalProductsReport').textContent = formatArabicNumber(totalProducts);
    document.getElementById('inventoryValueReport').textContent = formatCurrency(inventoryValue);
    document.getElementById('lowStockProductsReport').textContent = formatArabicNumber(lowStockProducts.length);

    // تحليل المخزون
    updateInventoryAnalysis(products);

    // المنتجات منخفضة المخزون
    updateLowStockProducts(lowStockProducts);
}

// تحديث تحليل المخزون
function updateInventoryAnalysis(products) {
    const categories = {};
    products.forEach(product => {
        const category = product.category || 'غير محدد';
        if (categories[category]) {
            categories[category].count++;
            categories[category].value += product.quantity * (product.cost || product.price);
        } else {
            categories[category] = {
                count: 1,
                value: product.quantity * (product.cost || product.price)
            };
        }
    });

    const analysisContent = document.getElementById('inventoryAnalysis');
    analysisContent.innerHTML = Object.keys(categories).map(category => `
        <div class="category-item">
            <div class="category-info">
                <span class="category-name">${category}</span>
                <span class="category-count">${formatArabicNumber(categories[category].count)} منتج</span>
            </div>
            <div class="category-value">${formatCurrency(categories[category].value)}</div>
        </div>
    `).join('');
}

// تحديث المنتجات منخفضة المخزون
function updateLowStockProducts(lowStockProducts) {
    const lowStockList = document.getElementById('lowStockProducts');

    if (lowStockProducts.length === 0) {
        lowStockList.innerHTML = '<div class="no-data">جميع المنتجات في مستوى مخزون جيد</div>';
    } else {
        lowStockList.innerHTML = lowStockProducts.map(product => `
            <div class="low-stock-item">
                <div class="product-info">
                    <span class="product-name">${product.name}</span>
                    <span class="product-category">${product.category || 'غير محدد'}</span>
                </div>
                <div class="stock-info">
                    <span class="current-stock">${formatArabicNumber(product.quantity)}</span>
                    <span class="min-stock">الحد الأدنى: ${formatArabicNumber(product.minStock)}</span>
                </div>
            </div>
        `).join('');
    }
}

// تحديث تقرير العملاء
function updateCustomersReport() {
    const customers = db.getData('customers').filter(customer => customer.name !== 'ضيف');
    const sales = getFilteredSales();

    const totalCustomers = customers.length;
    const activeCustomers = customers.filter(customer => {
        return sales.some(sale => sale.customerId === customer.id);
    }).length;

    const totalDebts = customers
        .filter(customer => customer.balance < 0)
        .reduce((sum, customer) => sum + Math.abs(customer.balance), 0);

    document.getElementById('totalCustomersReport').textContent = formatArabicNumber(totalCustomers);
    document.getElementById('activeCustomersReport').textContent = formatArabicNumber(activeCustomers);
    document.getElementById('totalDebtsReport').textContent = formatCurrency(totalDebts);

    // أفضل العملاء
    updateTopCustomers(sales, customers);

    // العملاء المدينون
    updateDebtorCustomers(customers);
}

// تحديث أفضل العملاء
function updateTopCustomers(sales, customers) {
    const customerSales = {};

    sales.forEach(sale => {
        if (customerSales[sale.customerId]) {
            customerSales[sale.customerId] += sale.total;
        } else {
            customerSales[sale.customerId] = sale.total;
        }
    });

    const topCustomers = Object.keys(customerSales)
        .map(customerId => {
            const customer = customers.find(c => c.id === customerId);
            return {
                customer: customer,
                total: customerSales[customerId]
            };
        })
        .filter(item => item.customer)
        .sort((a, b) => b.total - a.total)
        .slice(0, 5);

    const topCustomersList = document.getElementById('topCustomersList');
    if (topCustomers.length === 0) {
        topCustomersList.innerHTML = '<div class="no-data">لا توجد مبيعات في هذه الفترة</div>';
    } else {
        topCustomersList.innerHTML = topCustomers.map(item => `
            <div class="customer-item">
                <div class="customer-info">
                    <span class="customer-name">${item.customer.name}</span>
                    <span class="customer-phone">${item.customer.phone || 'لا يوجد هاتف'}</span>
                </div>
                <div class="customer-total">${formatCurrency(item.total)}</div>
            </div>
        `).join('');
    }
}

// تحديث العملاء المدينون
function updateDebtorCustomers(customers) {
    const debtors = customers
        .filter(customer => customer.balance < 0)
        .sort((a, b) => a.balance - b.balance)
        .slice(0, 5);

    const debtorCustomersList = document.getElementById('debtorCustomersList');
    if (debtors.length === 0) {
        debtorCustomersList.innerHTML = '<div class="no-data">لا توجد ديون</div>';
    } else {
        debtorCustomersList.innerHTML = debtors.map(customer => `
            <div class="debtor-item">
                <div class="customer-info">
                    <span class="customer-name">${customer.name}</span>
                    <span class="customer-phone">${customer.phone || 'لا يوجد هاتف'}</span>
                </div>
                <div class="debt-amount">${formatCurrency(Math.abs(customer.balance))}</div>
            </div>
        `).join('');
    }
}

// تحديث التقرير المالي
function updateFinancialReport() {
    const sales = getFilteredSales();
    const purchases = getFilteredPurchases();

    const totalSales = sales.reduce((sum, sale) => sum + sale.total, 0);
    const totalCOGS = sales.reduce((sum, sale) => {
        return sum + sale.items.reduce((itemSum, item) => {
            const product = db.getData('products').find(p => p.name === item.name);
            const cost = product ? (product.cost || product.price * 0.7) : item.price * 0.7;
            return itemSum + (cost * item.quantity);
        }, 0);
    }, 0);

    const grossProfit = totalSales - totalCOGS;
    const operatingExpenses = purchases.reduce((sum, purchase) => sum + purchase.total, 0);
    const netProfit = grossProfit - operatingExpenses;

    document.getElementById('financialSales').textContent = formatCurrency(totalSales);
    document.getElementById('financialCOGS').textContent = formatCurrency(totalCOGS);
    document.getElementById('financialGrossProfit').textContent = formatCurrency(grossProfit);
    document.getElementById('financialExpenses').textContent = formatCurrency(operatingExpenses);
    document.getElementById('financialNetProfit').textContent = formatCurrency(netProfit);

    // تحليل التدفق النقدي
    updateCashFlowAnalysis(sales, purchases);
}

// تحديث تحليل التدفق النقدي
function updateCashFlowAnalysis(sales, purchases) {
    const cashSales = sales.filter(sale => sale.paymentMethod === 'cash').reduce((sum, sale) => sum + sale.total, 0);
    const creditSales = sales.filter(sale => sale.paymentMethod === 'credit').reduce((sum, sale) => sum + sale.total, 0);
    const cashPurchases = purchases.filter(purchase => purchase.paymentMethod === 'cash').reduce((sum, purchase) => sum + purchase.total, 0);
    const creditPurchases = purchases.filter(purchase => purchase.paymentMethod === 'credit').reduce((sum, purchase) => sum + purchase.total, 0);

    const payments = db.getData('payments').filter(payment => {
        const paymentDate = new Date(payment.createdAt);
        return paymentDate >= reportDateRange.start && paymentDate <= reportDateRange.end;
    });

    const customerPayments = payments.filter(payment => payment.type === 'customer_payment').reduce((sum, payment) => sum + payment.amount, 0);
    const supplierPayments = payments.filter(payment => payment.type === 'supplier_payment').reduce((sum, payment) => sum + payment.amount, 0);

    const netCashFlow = (cashSales + customerPayments) - (cashPurchases + supplierPayments);

    document.getElementById('cashFlowAnalysis').innerHTML = `
        <div class="cash-flow-table">
            <div class="cash-flow-section">
                <h6>التدفقات النقدية الداخلة</h6>
                <div class="cash-flow-row">
                    <span class="label">مبيعات نقدية:</span>
                    <span class="value positive">${formatCurrency(cashSales)}</span>
                </div>
                <div class="cash-flow-row">
                    <span class="label">تحصيلات من العملاء:</span>
                    <span class="value positive">${formatCurrency(customerPayments)}</span>
                </div>
                <div class="cash-flow-row total">
                    <span class="label">إجمالي التدفقات الداخلة:</span>
                    <span class="value positive">${formatCurrency(cashSales + customerPayments)}</span>
                </div>
            </div>

            <div class="cash-flow-section">
                <h6>التدفقات النقدية الخارجة</h6>
                <div class="cash-flow-row">
                    <span class="label">مشتريات نقدية:</span>
                    <span class="value negative">${formatCurrency(cashPurchases)}</span>
                </div>
                <div class="cash-flow-row">
                    <span class="label">دفعات للموردين:</span>
                    <span class="value negative">${formatCurrency(supplierPayments)}</span>
                </div>
                <div class="cash-flow-row total">
                    <span class="label">إجمالي التدفقات الخارجة:</span>
                    <span class="value negative">${formatCurrency(cashPurchases + supplierPayments)}</span>
                </div>
            </div>

            <div class="cash-flow-section">
                <div class="cash-flow-row net-flow">
                    <span class="label">صافي التدفق النقدي:</span>
                    <span class="value ${netCashFlow >= 0 ? 'positive' : 'negative'}">${formatCurrency(netCashFlow)}</span>
                </div>
            </div>
        </div>
    `;
}

// عرض تبويب التقرير
function showReportTab(tabName) {
    // إخفاء جميع التبويبات
    document.querySelectorAll('.report-tab').forEach(tab => {
        tab.classList.add('hidden');
    });

    // إزالة الفئة النشطة من جميع الأزرار
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.classList.remove('active');
    });

    // عرض التبويب المحدد
    document.getElementById(tabName + 'Report').classList.remove('hidden');

    // إضافة الفئة النشطة للزر المحدد
    document.querySelector(`[onclick="showReportTab('${tabName}')"]`).classList.add('active');
}

// تصدير تقرير المبيعات
function exportSalesReport() {
    try {
        const sales = getFilteredSales();
        const csvContent = generateSalesReportCSV(sales);
        downloadCSV(csvContent, 'sales-report.csv');
        showAlert('تم تصدير تقرير المبيعات بنجاح', 'success');
    } catch (error) {
        showAlert('حدث خطأ أثناء تصدير تقرير المبيعات', 'error');
    }
}

// تصدير تقرير المخزون
function exportInventoryReport() {
    try {
        const products = db.getData('products');
        const csvContent = generateInventoryReportCSV(products);
        downloadCSV(csvContent, 'inventory-report.csv');
        showAlert('تم تصدير تقرير المخزون بنجاح', 'success');
    } catch (error) {
        showAlert('حدث خطأ أثناء تصدير تقرير المخزون', 'error');
    }
}

// تصدير تقرير العملاء
function exportCustomersReport() {
    try {
        const customers = db.getData('customers').filter(customer => customer.name !== 'ضيف');
        const csvContent = generateCustomersReportCSV(customers);
        downloadCSV(csvContent, 'customers-report.csv');
        showAlert('تم تصدير تقرير العملاء بنجاح', 'success');
    } catch (error) {
        showAlert('حدث خطأ أثناء تصدير تقرير العملاء', 'error');
    }
}

// تصدير التقرير المالي
function exportFinancialReport() {
    try {
        const sales = getFilteredSales();
        const purchases = getFilteredPurchases();
        const csvContent = generateFinancialReportCSV(sales, purchases);
        downloadCSV(csvContent, 'financial-report.csv');
        showAlert('تم تصدير التقرير المالي بنجاح', 'success');
    } catch (error) {
        showAlert('حدث خطأ أثناء تصدير التقرير المالي', 'error');
    }
}

// تصدير جميع التقارير
function exportAllReports() {
    try {
        exportSalesReport();
        setTimeout(() => exportInventoryReport(), 500);
        setTimeout(() => exportCustomersReport(), 1000);
        setTimeout(() => exportFinancialReport(), 1500);
        showAlert('تم بدء تصدير جميع التقارير', 'info');
    } catch (error) {
        showAlert('حدث خطأ أثناء تصدير التقارير', 'error');
    }
}

// توليد ملف CSV لتقرير المبيعات
function generateSalesReportCSV(sales) {
    const headers = ['رقم الفاتورة', 'العميل', 'المجموع', 'طريقة الدفع', 'التاريخ'];
    const rows = sales.map(sale => [
        sale.invoiceNumber,
        sale.customerName,
        sale.total,
        sale.paymentMethod === 'cash' ? 'نقداً' : 'آجل',
        new Date(sale.createdAt).toLocaleDateString('ar-SA')
    ]);

    return [headers, ...rows].map(row => row.join(',')).join('\n');
}

// توليد ملف CSV لتقرير المخزون
function generateInventoryReportCSV(products) {
    const headers = ['اسم المنتج', 'الفئة', 'الكمية', 'السعر', 'التكلفة', 'القيمة الإجمالية'];
    const rows = products.map(product => [
        product.name,
        product.category || 'غير محدد',
        product.quantity,
        product.price,
        product.cost || product.price * 0.7,
        product.quantity * (product.cost || product.price)
    ]);

    return [headers, ...rows].map(row => row.join(',')).join('\n');
}

// توليد ملف CSV لتقرير العملاء
function generateCustomersReportCSV(customers) {
    const headers = ['اسم العميل', 'الهاتف', 'البريد الإلكتروني', 'الرصيد', 'النوع'];
    const rows = customers.map(customer => [
        customer.name,
        customer.phone || '',
        customer.email || '',
        customer.balance,
        customer.type === 'cash' ? 'نقدي' : 'آجل'
    ]);

    return [headers, ...rows].map(row => row.join(',')).join('\n');
}

// توليد ملف CSV للتقرير المالي
function generateFinancialReportCSV(sales, purchases) {
    const totalSales = sales.reduce((sum, sale) => sum + sale.total, 0);
    const totalPurchases = purchases.reduce((sum, purchase) => sum + purchase.total, 0);
    const profit = totalSales - totalPurchases;

    const headers = ['البيان', 'المبلغ'];
    const rows = [
        ['إجمالي المبيعات', totalSales],
        ['إجمالي المشتريات', totalPurchases],
        ['صافي الربح', profit],
        ['هامش الربح %', totalSales > 0 ? ((profit / totalSales) * 100).toFixed(2) : 0]
    ];

    return [headers, ...rows].map(row => row.join(',')).join('\n');
}
