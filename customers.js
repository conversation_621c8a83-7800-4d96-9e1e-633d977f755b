// إدارة العملاء
function getCustomersHTML() {
    return `
        <div class="customers-container">
            <div class="page-header">
                <h2 class="page-title">إدارة العملاء</h2>
                <div class="page-actions">
                    <button class="btn btn-primary" onclick="showAddCustomerModal()">
                        <i class="fas fa-plus"></i>
                        إضافة عميل جديد
                    </button>
                    <button class="btn btn-success" onclick="exportCustomers()">
                        <i class="fas fa-download"></i>
                        تصدير
                    </button>
                </div>
            </div>

            <!-- شريط البحث والفلترة -->
            <div class="search-filter-bar">
                <div class="search-group">
                    <div class="input-group">
                        <input type="text" id="customerSearch" placeholder="البحث في العملاء..." onkeyup="searchCustomers()">
                        <i class="fas fa-search"></i>
                    </div>
                </div>
                <div class="filter-group">
                    <select id="balanceFilter" onchange="filterCustomers()">
                        <option value="">جميع العملاء</option>
                        <option value="debtors">العملاء المدينون</option>
                        <option value="creditors">العملاء الدائنون</option>
                        <option value="zero">رصيد صفر</option>
                    </select>
                </div>
            </div>

            <!-- جدول العملاء -->
            <div class="table-container">
                <table class="data-table" id="customersTable">
                    <thead>
                        <tr>
                            <th>الاسم</th>
                            <th>الهاتف</th>
                            <th>البريد الإلكتروني</th>
                            <th>العنوان</th>
                            <th>الرصيد</th>
                            <th>النوع</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="customersTableBody">
                        <!-- سيتم ملء البيانات هنا -->
                    </tbody>
                </table>
            </div>

            <!-- إحصائيات العملاء -->
            <div class="customers-stats">
                <div class="stat-item">
                    <span class="label">إجمالي العملاء:</span>
                    <span class="value" id="totalCustomersCount">٠</span>
                </div>
                <div class="stat-item">
                    <span class="label">العملاء المدينون:</span>
                    <span class="value" id="debtorCustomersCount">٠</span>
                </div>
                <div class="stat-item">
                    <span class="label">إجمالي الديون:</span>
                    <span class="value" id="totalDebtsAmount">٠.٠٠ ريال</span>
                </div>
            </div>
        </div>

        <!-- نافذة إضافة/تعديل عميل -->
        <div id="customerModal" class="modal hidden">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 id="customerModalTitle">إضافة عميل جديد</h3>
                    <button class="btn btn-icon" onclick="hideCustomerModal()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="customerForm">
                        <div class="form-group">
                            <label for="customerName">اسم العميل *</label>
                            <input type="text" id="customerName" required>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="customerPhone">رقم الهاتف</label>
                                <input type="tel" id="customerPhone">
                            </div>
                            <div class="form-group">
                                <label for="customerEmail">البريد الإلكتروني</label>
                                <input type="email" id="customerEmail">
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="customerAddress">العنوان</label>
                            <textarea id="customerAddress" rows="3"></textarea>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="customerBalance">الرصيد الابتدائي</label>
                                <input type="number" id="customerBalance" step="0.01" value="0">
                            </div>
                            <div class="form-group">
                                <label for="customerType">نوع العميل</label>
                                <select id="customerType">
                                    <option value="cash">عميل نقدي</option>
                                    <option value="credit">عميل آجل</option>
                                </select>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" onclick="hideCustomerModal()">إلغاء</button>
                    <button class="btn btn-primary" onclick="saveCustomer()">حفظ</button>
                </div>
            </div>
        </div>

        <!-- نافذة تفاصيل العميل -->
        <div id="customerDetailsModal" class="modal hidden">
            <div class="modal-content large-modal">
                <div class="modal-header">
                    <h3>تفاصيل العميل</h3>
                    <button class="btn btn-icon" onclick="hideCustomerDetailsModal()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div id="customerDetailsContent">
                        <!-- سيتم ملء التفاصيل هنا -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-info" onclick="printCustomerStatement()">
                        <i class="fas fa-print"></i>
                        طباعة كشف الحساب
                    </button>
                    <button class="btn btn-secondary" onclick="hideCustomerDetailsModal()">إغلاق</button>
                </div>
            </div>
        </div>

        <!-- نافذة إضافة دفعة -->
        <div id="paymentModal" class="modal hidden">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>إضافة دفعة</h3>
                    <button class="btn btn-icon" onclick="hidePaymentModal()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="paymentForm">
                        <div class="form-group">
                            <label for="paymentAmount">مبلغ الدفعة *</label>
                            <input type="number" id="paymentAmount" step="0.01" min="0" required>
                        </div>
                        <div class="form-group">
                            <label for="paymentNotes">ملاحظات</label>
                            <textarea id="paymentNotes" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" onclick="hidePaymentModal()">إلغاء</button>
                    <button class="btn btn-primary" onclick="savePayment()">حفظ الدفعة</button>
                </div>
            </div>
        </div>
    `;
}

// متغيرات العملاء
let currentCustomerId = null;
let customersData = [];

// تهيئة صفحة العملاء
function initializeCustomers() {
    loadCustomers();
    updateCustomersStats();
}

// تحميل العملاء
function loadCustomers() {
    customersData = db.getData('customers');
    displayCustomers(customersData);
}

// عرض العملاء في الجدول
function displayCustomers(customers) {
    const tbody = document.getElementById('customersTableBody');
    
    if (customers.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="7" class="text-center">لا توجد عملاء</td>
            </tr>
        `;
        return;
    }
    
    tbody.innerHTML = customers.map(customer => {
        const balanceStatus = getBalanceStatus(customer.balance);
        return `
            <tr>
                <td>
                    <div class="customer-info">
                        <strong>${customer.name}</strong>
                        ${customer.name === 'ضيف' ? '<br><small class="text-muted">عميل افتراضي</small>' : ''}
                    </div>
                </td>
                <td>${customer.phone || '-'}</td>
                <td>${customer.email || '-'}</td>
                <td>${customer.address || '-'}</td>
                <td>
                    <span class="balance-amount ${balanceStatus.class}">
                        ${formatCurrency(customer.balance)}
                    </span>
                </td>
                <td>
                    <span class="customer-type ${customer.type}">
                        ${customer.type === 'cash' ? 'نقدي' : 'آجل'}
                    </span>
                </td>
                <td>
                    <div class="action-buttons">
                        <button class="btn btn-sm btn-info" onclick="showCustomerDetails('${customer.id}')" title="عرض التفاصيل">
                            <i class="fas fa-eye"></i>
                        </button>
                        ${customer.name !== 'ضيف' ? `
                            <button class="btn btn-sm btn-success" onclick="showPaymentModal('${customer.id}')" title="إضافة دفعة">
                                <i class="fas fa-money-bill"></i>
                            </button>
                            <button class="btn btn-sm btn-warning" onclick="editCustomer('${customer.id}')" title="تعديل">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-danger" onclick="deleteCustomer('${customer.id}')" title="حذف">
                                <i class="fas fa-trash"></i>
                            </button>
                        ` : ''}
                    </div>
                </td>
            </tr>
        `;
    }).join('');
}

// تحديد حالة الرصيد
function getBalanceStatus(balance) {
    if (balance < 0) {
        return { class: 'balance-negative', text: 'مدين' };
    } else if (balance > 0) {
        return { class: 'balance-positive', text: 'دائن' };
    } else {
        return { class: 'balance-zero', text: 'صفر' };
    }
}

// البحث في العملاء
function searchCustomers() {
    const searchTerm = document.getElementById('customerSearch').value.toLowerCase();
    const filteredCustomers = customersData.filter(customer =>
        customer.name.toLowerCase().includes(searchTerm) ||
        (customer.phone && customer.phone.includes(searchTerm)) ||
        (customer.email && customer.email.toLowerCase().includes(searchTerm)) ||
        (customer.address && customer.address.toLowerCase().includes(searchTerm))
    );
    displayCustomers(filteredCustomers);
}

// فلترة العملاء
function filterCustomers() {
    const balanceFilter = document.getElementById('balanceFilter').value;
    
    let filteredCustomers = customersData;
    
    if (balanceFilter === 'debtors') {
        filteredCustomers = filteredCustomers.filter(customer => customer.balance < 0);
    } else if (balanceFilter === 'creditors') {
        filteredCustomers = filteredCustomers.filter(customer => customer.balance > 0);
    } else if (balanceFilter === 'zero') {
        filteredCustomers = filteredCustomers.filter(customer => customer.balance === 0);
    }
    
    displayCustomers(filteredCustomers);
}

// تحديث إحصائيات العملاء
function updateCustomersStats() {
    const customers = db.getData('customers');
    const totalCustomers = customers.length - 1; // استثناء عميل "ضيف"
    const debtorCustomers = customers.filter(customer => customer.balance < 0).length;
    const totalDebts = customers
        .filter(customer => customer.balance < 0)
        .reduce((sum, customer) => sum + Math.abs(customer.balance), 0);

    document.getElementById('totalCustomersCount').textContent = formatArabicNumber(totalCustomers);
    document.getElementById('debtorCustomersCount').textContent = formatArabicNumber(debtorCustomers);
    document.getElementById('totalDebtsAmount').textContent = formatCurrency(totalDebts);
}

// عرض نافذة إضافة عميل
function showAddCustomerModal() {
    currentCustomerId = null;
    document.getElementById('customerModalTitle').textContent = 'إضافة عميل جديد';
    document.getElementById('customerForm').reset();
    document.getElementById('customerBalance').value = '0';
    document.getElementById('customerModal').classList.remove('hidden');
}

// إخفاء نافذة العميل
function hideCustomerModal() {
    document.getElementById('customerModal').classList.add('hidden');
    currentCustomerId = null;
}

// تعديل عميل
function editCustomer(customerId) {
    const customer = customersData.find(c => c.id === customerId);
    if (!customer || customer.name === 'ضيف') return;

    currentCustomerId = customerId;
    document.getElementById('customerModalTitle').textContent = 'تعديل العميل';

    // ملء النموذج بالبيانات
    document.getElementById('customerName').value = customer.name;
    document.getElementById('customerPhone').value = customer.phone || '';
    document.getElementById('customerEmail').value = customer.email || '';
    document.getElementById('customerAddress').value = customer.address || '';
    document.getElementById('customerBalance').value = customer.balance;
    document.getElementById('customerType').value = customer.type;

    document.getElementById('customerModal').classList.remove('hidden');
}

// حفظ العميل
function saveCustomer() {
    const form = document.getElementById('customerForm');
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    const email = document.getElementById('customerEmail').value;
    const phone = document.getElementById('customerPhone').value;

    // التحقق من صحة البريد الإلكتروني
    if (email && !validateEmail(email)) {
        showAlert('البريد الإلكتروني غير صحيح', 'warning');
        return;
    }

    // التحقق من صحة رقم الهاتف
    if (phone && !validatePhone(phone)) {
        showAlert('رقم الهاتف غير صحيح', 'warning');
        return;
    }

    const customerData = {
        name: sanitizeInput(document.getElementById('customerName').value),
        phone: sanitizeInput(phone),
        email: sanitizeInput(email),
        address: sanitizeInput(document.getElementById('customerAddress').value),
        balance: parseFloat(document.getElementById('customerBalance').value) || 0,
        type: document.getElementById('customerType').value
    };

    try {
        if (currentCustomerId) {
            // تحديث عميل موجود
            db.updateItem('customers', currentCustomerId, customerData);
            showAlert('تم تحديث العميل بنجاح', 'success');
        } else {
            // إضافة عميل جديد
            db.addItem('customers', customerData);
            showAlert('تم إضافة العميل بنجاح', 'success');
        }

        hideCustomerModal();
        loadCustomers();
        updateCustomersStats();
        updateStats();

    } catch (error) {
        showAlert('حدث خطأ أثناء حفظ العميل', 'error');
    }
}

// حذف عميل
function deleteCustomer(customerId) {
    const customer = customersData.find(c => c.id === customerId);
    if (!customer || customer.name === 'ضيف') return;

    // التحقق من وجود معاملات للعميل
    const sales = db.getData('sales');
    const customerSales = sales.filter(sale => sale.customerId === customerId);

    if (customerSales.length > 0) {
        showAlert('لا يمكن حذف العميل لوجود معاملات مرتبطة به', 'warning');
        return;
    }

    showConfirm(`هل أنت متأكد من حذف العميل "${customer.name}"؟`, function() {
        try {
            db.deleteItem('customers', customerId);
            showAlert('تم حذف العميل بنجاح', 'success');
            loadCustomers();
            updateCustomersStats();
            updateStats();
        } catch (error) {
            showAlert('حدث خطأ أثناء حذف العميل', 'error');
        }
    });
}

// عرض تفاصيل العميل
function showCustomerDetails(customerId) {
    const customer = customersData.find(c => c.id === customerId);
    if (!customer) return;

    // الحصول على معاملات العميل
    const sales = db.getData('sales').filter(sale => sale.customerId === customerId);
    const payments = db.getData('payments').filter(payment => payment.customerId === customerId);

    // حساب إجمالي المبيعات والدفعات
    const totalSales = sales.reduce((sum, sale) => sum + sale.total, 0);
    const totalPayments = payments.reduce((sum, payment) => sum + payment.amount, 0);

    const balanceStatus = getBalanceStatus(customer.balance);

    document.getElementById('customerDetailsContent').innerHTML = `
        <div class="customer-details">
            <div class="customer-info-section">
                <h4>معلومات العميل</h4>
                <div class="detail-row">
                    <span class="detail-label">الاسم:</span>
                    <span class="detail-value">${customer.name}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">الهاتف:</span>
                    <span class="detail-value">${customer.phone || 'غير محدد'}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">البريد الإلكتروني:</span>
                    <span class="detail-value">${customer.email || 'غير محدد'}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">العنوان:</span>
                    <span class="detail-value">${customer.address || 'غير محدد'}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">نوع العميل:</span>
                    <span class="detail-value">${customer.type === 'cash' ? 'نقدي' : 'آجل'}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">الرصيد الحالي:</span>
                    <span class="detail-value">
                        <span class="balance-amount ${balanceStatus.class}">
                            ${formatCurrency(customer.balance)}
                        </span>
                    </span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">تاريخ الإضافة:</span>
                    <span class="detail-value">${formatDateTime(customer.createdAt)}</span>
                </div>
            </div>

            <div class="customer-stats-section">
                <h4>إحصائيات العميل</h4>
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-label">إجمالي المبيعات</div>
                        <div class="stat-value">${formatCurrency(totalSales)}</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-label">إجمالي الدفعات</div>
                        <div class="stat-value">${formatCurrency(totalPayments)}</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-label">عدد الفواتير</div>
                        <div class="stat-value">${formatArabicNumber(sales.length)}</div>
                    </div>
                </div>
            </div>

            <div class="customer-transactions-section">
                <h4>آخر المعاملات</h4>
                <div class="transactions-list">
                    ${sales.slice(-5).reverse().map(sale => `
                        <div class="transaction-item">
                            <div class="transaction-info">
                                <span class="transaction-type">فاتورة مبيعات</span>
                                <span class="transaction-number">${sale.invoiceNumber}</span>
                            </div>
                            <div class="transaction-amount">${formatCurrency(sale.total)}</div>
                            <div class="transaction-date">${formatDateTime(sale.createdAt)}</div>
                        </div>
                    `).join('')}
                    ${payments.slice(-5).reverse().map(payment => `
                        <div class="transaction-item">
                            <div class="transaction-info">
                                <span class="transaction-type">دفعة</span>
                                <span class="transaction-notes">${payment.notes || ''}</span>
                            </div>
                            <div class="transaction-amount positive">${formatCurrency(payment.amount)}</div>
                            <div class="transaction-date">${formatDateTime(payment.createdAt)}</div>
                        </div>
                    `).join('')}
                    ${(sales.length === 0 && payments.length === 0) ? '<div class="no-transactions">لا توجد معاملات</div>' : ''}
                </div>
            </div>
        </div>
    `;

    document.getElementById('customerDetailsModal').classList.remove('hidden');
}

// إخفاء تفاصيل العميل
function hideCustomerDetailsModal() {
    document.getElementById('customerDetailsModal').classList.add('hidden');
}

// عرض نافذة إضافة دفعة
function showPaymentModal(customerId) {
    const customer = customersData.find(c => c.id === customerId);
    if (!customer || customer.name === 'ضيف') return;

    currentCustomerId = customerId;
    document.getElementById('paymentForm').reset();
    document.getElementById('paymentModal').classList.remove('hidden');
}

// إخفاء نافذة الدفعة
function hidePaymentModal() {
    document.getElementById('paymentModal').classList.add('hidden');
    currentCustomerId = null;
}

// حفظ الدفعة
function savePayment() {
    const form = document.getElementById('paymentForm');
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    const amount = parseFloat(document.getElementById('paymentAmount').value);
    const notes = sanitizeInput(document.getElementById('paymentNotes').value);

    if (amount <= 0) {
        showAlert('مبلغ الدفعة يجب أن يكون أكبر من صفر', 'warning');
        return;
    }

    const paymentData = {
        customerId: currentCustomerId,
        amount: amount,
        notes: notes,
        type: 'customer_payment'
    };

    try {
        // حفظ الدفعة
        db.addItem('payments', paymentData);

        // تحديث رصيد العميل
        const customer = db.getData('customers').find(c => c.id === currentCustomerId);
        if (customer) {
            const newBalance = customer.balance + amount;
            db.updateItem('customers', currentCustomerId, { balance: newBalance });
        }

        showAlert('تم إضافة الدفعة بنجاح', 'success');
        hidePaymentModal();
        loadCustomers();
        updateCustomersStats();
        updateStats();

    } catch (error) {
        showAlert('حدث خطأ أثناء إضافة الدفعة', 'error');
    }
}

// طباعة كشف حساب العميل
function printCustomerStatement() {
    const content = document.getElementById('customerDetailsContent').innerHTML;
    printContent(content, 'كشف حساب العميل');
}

// تصدير العملاء
function exportCustomers() {
    try {
        const customers = db.getData('customers');
        const csvContent = generateCustomersCSV(customers);
        downloadCSV(csvContent, 'customers.csv');
        showAlert('تم تصدير العملاء بنجاح', 'success');
    } catch (error) {
        showAlert('حدث خطأ أثناء تصدير العملاء', 'error');
    }
}

// توليد ملف CSV للعملاء
function generateCustomersCSV(customers) {
    const headers = ['الاسم', 'الهاتف', 'البريد الإلكتروني', 'العنوان', 'الرصيد', 'النوع'];
    const rows = customers.map(customer => [
        customer.name,
        customer.phone || '',
        customer.email || '',
        customer.address || '',
        customer.balance,
        customer.type === 'cash' ? 'نقدي' : 'آجل'
    ]);

    return [headers, ...rows].map(row => row.join(',')).join('\n');
}
