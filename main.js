// المتغيرات العامة
let currentUser = null;
let currentPage = 'dashboard';
let confirmCallback = null;

// تهيئة التطبيق
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

// تهيئة التطبيق
function initializeApp() {
    // التحقق من حالة تسجيل الدخول
    const isLoggedIn = sessionStorage.getItem('erp_logged_in');
    
    if (isLoggedIn) {
        showMainApp();
        loadPage('dashboard');
    } else {
        showLoginScreen();
    }

    // إضافة مستمعي الأحداث
    setupEventListeners();
    
    // تطبيق الثيم المحفوظ
    applyTheme();
}

// إعداد مستمعي الأحداث
function setupEventListeners() {
    // تسجيل الدخول
    const loginForm = document.getElementById('loginForm');
    if (loginForm) {
        loginForm.addEventListener('submit', handleLogin);
    }

    // التنقل بين الصفحات
    const navItems = document.querySelectorAll('.nav-item');
    navItems.forEach(item => {
        item.addEventListener('click', function(e) {
            e.preventDefault();
            const page = this.getAttribute('data-page');
            loadPage(page);
        });
    });

    // إغلاق النوافذ المنبثقة عند النقر خارجها
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('modal')) {
            hideAllModals();
        }
    });

    // اختصارات لوحة المفاتيح
    document.addEventListener('keydown', handleKeyboardShortcuts);
}

// معالجة تسجيل الدخول
function handleLogin(e) {
    e.preventDefault();
    
    const password = document.getElementById('passwordInput').value;
    
    if (db.verifyPassword(password)) {
        sessionStorage.setItem('erp_logged_in', 'true');
        showMainApp();
        loadPage('dashboard');
        showAlert('مرحباً بك في نظام إدارة نقاط البيع', 'success');
    } else {
        showAlert('كلمة المرور غير صحيحة', 'error');
        document.getElementById('passwordInput').value = '';
    }
}

// تسجيل الخروج
function handleLogout() {
    showConfirm('هل أنت متأكد من تسجيل الخروج؟', function() {
        sessionStorage.removeItem('erp_logged_in');
        showLoginScreen();
        document.getElementById('passwordInput').value = '';
    });
}

// عرض شاشة تسجيل الدخول
function showLoginScreen() {
    document.getElementById('loginScreen').classList.remove('hidden');
    document.getElementById('mainApp').classList.add('hidden');
}

// عرض التطبيق الرئيسي
function showMainApp() {
    document.getElementById('loginScreen').classList.add('hidden');
    document.getElementById('mainApp').classList.remove('hidden');
}

// تحميل صفحة
function loadPage(pageName) {
    currentPage = pageName;
    
    // تحديث التنقل النشط
    document.querySelectorAll('.nav-item').forEach(item => {
        item.classList.remove('active');
    });
    document.querySelector(`[data-page="${pageName}"]`).classList.add('active');
    
    // تحميل محتوى الصفحة
    const pageContent = document.getElementById('pageContent');
    
    switch(pageName) {
        case 'dashboard':
            pageContent.innerHTML = getDashboardHTML();
            initializeDashboard();
            break;
        case 'sales':
            pageContent.innerHTML = getSalesHTML();
            initializeSales();
            break;
        case 'products':
            pageContent.innerHTML = getProductsHTML();
            initializeProducts();
            break;
        case 'categories':
            pageContent.innerHTML = getCategoriesHTML();
            initializeCategories();
            break;
        case 'customers':
            pageContent.innerHTML = getCustomersHTML();
            initializeCustomers();
            break;
        case 'suppliers':
            pageContent.innerHTML = getSuppliersHTML();
            initializeSuppliers();
            break;
        case 'purchases':
            pageContent.innerHTML = getPurchasesHTML();
            initializePurchases();
            break;
        case 'debts':
            pageContent.innerHTML = getDebtsHTML();
            initializeDebts();
            break;
        case 'expenses':
            pageContent.innerHTML = getExpensesHTML();
            initializeExpenses();
            break;
        case 'reports':
            pageContent.innerHTML = getReportsHTML();
            initializeReports();
            break;
        case 'settings':
            pageContent.innerHTML = getSettingsHTML();
            initializeSettings();
            break;
        default:
            pageContent.innerHTML = '<div class="text-center"><h2>الصفحة غير موجودة</h2></div>';
    }
}

// تبديل الثيم
function toggleTheme() {
    const currentTheme = document.documentElement.getAttribute('data-theme');
    const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
    
    document.documentElement.setAttribute('data-theme', newTheme);
    
    // حفظ الثيم في الإعدادات
    const settings = db.getSettings();
    settings.theme = newTheme;
    db.saveSettings(settings);
    
    // تحديث أيقونة الثيم
    const themeIcon = document.querySelector('.btn-icon i');
    themeIcon.className = newTheme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
}

// تطبيق الثيم
function applyTheme() {
    const settings = db.getSettings();
    const theme = settings.theme || 'light';
    document.documentElement.setAttribute('data-theme', theme);
    
    // تحديث أيقونة الثيم
    const themeIcon = document.querySelector('.btn-icon i');
    if (themeIcon) {
        themeIcon.className = theme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
    }
}

// عرض تنبيه
function showAlert(message, type = 'info', title = 'تنبيه') {
    const alertModal = document.getElementById('alertModal');
    const alertTitle = document.getElementById('alertTitle');
    const alertMessage = document.getElementById('alertMessage');
    
    alertTitle.textContent = title;
    alertMessage.textContent = message;
    
    // تغيير لون التنبيه حسب النوع
    const modalContent = alertModal.querySelector('.modal-content');
    modalContent.className = 'modal-content alert-modal';
    
    if (type === 'success') {
        modalContent.classList.add('alert-success');
    } else if (type === 'error') {
        modalContent.classList.add('alert-error');
    } else if (type === 'warning') {
        modalContent.classList.add('alert-warning');
    }
    
    alertModal.classList.remove('hidden');
}

// إخفاء تنبيه
function hideAlertModal() {
    document.getElementById('alertModal').classList.add('hidden');
}

// عرض تأكيد
function showConfirm(message, callback, title = 'تأكيد') {
    const confirmModal = document.getElementById('confirmModal');
    const confirmTitle = document.getElementById('confirmTitle');
    const confirmMessage = document.getElementById('confirmMessage');
    
    confirmTitle.textContent = title;
    confirmMessage.textContent = message;
    confirmCallback = callback;
    
    confirmModal.classList.remove('hidden');
}

// إخفاء تأكيد
function hideConfirmModal() {
    document.getElementById('confirmModal').classList.add('hidden');
    confirmCallback = null;
}

// معالجة موافقة التأكيد
function handleConfirmYes() {
    if (confirmCallback) {
        confirmCallback();
    }
    hideConfirmModal();
}

// إخفاء جميع النوافذ المنبثقة
function hideAllModals() {
    document.querySelectorAll('.modal').forEach(modal => {
        modal.classList.add('hidden');
    });
}

// معالجة اختصارات لوحة المفاتيح
function handleKeyboardShortcuts(e) {
    // Escape لإغلاق النوافذ المنبثقة
    if (e.key === 'Escape') {
        hideAllModals();
    }
    
    // Ctrl + S للحفظ السريع
    if (e.ctrlKey && e.key === 's') {
        e.preventDefault();
        // يمكن إضافة وظيفة الحفظ السريع هنا
    }
    
    // F1 للمساعدة
    if (e.key === 'F1') {
        e.preventDefault();
        showAlert('اضغط Escape لإغلاق النوافذ المنبثقة\nCtrl+S للحفظ السريع', 'info', 'المساعدة');
    }
}

// تنسيق الأرقام العربية
function formatArabicNumber(number) {
    return db.toArabicNumbers(number.toLocaleString('ar-SA'));
}

// تنسيق العملة
function formatCurrency(amount) {
    return db.formatCurrency(amount);
}

// تنسيق التاريخ
function formatDate(date) {
    return db.formatDate(date);
}

// تنسيق التاريخ والوقت
function formatDateTime(date) {
    return db.formatDateTime(date);
}

// التحقق من صحة البريد الإلكتروني
function validateEmail(email) {
    const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return re.test(email);
}

// التحقق من صحة رقم الهاتف
function validatePhone(phone) {
    const re = /^[\d\u0660-\u0669\+\-\s\(\)]+$/;
    return re.test(phone);
}

// تنظيف النص
function sanitizeInput(input) {
    return input.toString().trim().replace(/[<>]/g, '');
}

// تحديث الإحصائيات في الوقت الفعلي
function updateStats() {
    // سيتم استدعاء هذه الدالة عند تحديث البيانات
    if (currentPage === 'dashboard') {
        initializeDashboard();
    }
}

// حفظ تلقائي للبيانات
function autoSave() {
    // يمكن إضافة منطق الحفظ التلقائي هنا
    console.log('تم الحفظ التلقائي في:', new Date().toLocaleString('ar-SA'));
}

// تشغيل الحفظ التلقائي كل 5 دقائق
setInterval(autoSave, 5 * 60 * 1000);

// تصدير البيانات
function exportData() {
    try {
        const data = db.exportData();
        const blob = new Blob([data], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `erp-backup-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        showAlert('تم تصدير البيانات بنجاح', 'success');
    } catch (error) {
        showAlert('حدث خطأ أثناء تصدير البيانات', 'error');
    }
}

// استيراد البيانات
function importData(file) {
    const reader = new FileReader();
    reader.onload = function(e) {
        try {
            const success = db.importData(e.target.result);
            if (success) {
                showAlert('تم استيراد البيانات بنجاح', 'success');
                loadPage(currentPage); // إعادة تحميل الصفحة الحالية
            } else {
                showAlert('فشل في استيراد البيانات', 'error');
            }
        } catch (error) {
            showAlert('ملف البيانات غير صحيح', 'error');
        }
    };
    reader.readAsText(file);
}

// طباعة المحتوى
function printContent(content, title = 'طباعة') {
    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <title>${title}</title>
            <style>
                body { font-family: 'Cairo', sans-serif; direction: rtl; }
                .print-header { text-align: center; margin-bottom: 20px; }
                .print-content { margin: 20px; }
                @media print {
                    body { margin: 0; }
                    .no-print { display: none; }
                }
            </style>
        </head>
        <body>
            <div class="print-header">
                <h1>${title}</h1>
                <p>تاريخ الطباعة: ${formatDateTime(new Date())}</p>
            </div>
            <div class="print-content">
                ${content}
            </div>
        </body>
        </html>
    `);
    printWindow.document.close();
    printWindow.print();
}
