// نظام المبيعات
function getSalesHTML() {
    return `
        <div class="sales-container">
            <div class="page-header">
                <h2 class="page-title">نظام المبيعات</h2>
                <div class="page-actions">
                    <button class="btn btn-success" onclick="showSalesHistory()">
                        <i class="fas fa-history"></i>
                        تاريخ المبيعات
                    </button>
                </div>
            </div>

            <div class="sales-layout">
                <!-- قسم المنتجات -->
                <div class="products-section">
                    <div class="section-header">
                        <h3>المنتجات</h3>
                        <div class="search-group">
                            <div class="input-group">
                                <input type="text" id="productSearchSales" placeholder="البحث في المنتجات..." onkeyup="searchSalesProducts()">
                                <i class="fas fa-search"></i>
                            </div>
                        </div>
                    </div>
                    
                    <div class="products-grid" id="salesProductsGrid">
                        <!-- سيتم ملء المنتجات هنا -->
                    </div>
                </div>

                <!-- قسم الفاتورة -->
                <div class="invoice-section">
                    <div class="section-header">
                        <h3>الفاتورة</h3>
                        <div class="invoice-actions">
                            <button class="btn btn-sm btn-warning" onclick="clearCart()">
                                <i class="fas fa-trash"></i>
                                مسح الكل
                            </button>
                        </div>
                    </div>

                    <!-- معلومات العميل -->
                    <div class="customer-section">
                        <div class="form-group">
                            <label for="customerSelect">العميل</label>
                            <select id="customerSelect" onchange="updateCustomerInfo()">
                                <option value="">اختر العميل</option>
                            </select>
                        </div>
                        <div id="customerInfo" class="customer-info hidden">
                            <div class="info-item">
                                <span class="label">الرصيد الحالي:</span>
                                <span class="value" id="customerBalance">٠.٠٠ ريال</span>
                            </div>
                        </div>
                    </div>

                    <!-- عناصر الفاتورة -->
                    <div class="cart-items" id="cartItems">
                        <div class="empty-cart">
                            <i class="fas fa-shopping-cart"></i>
                            <p>لا توجد عناصر في الفاتورة</p>
                        </div>
                    </div>

                    <!-- ملخص الفاتورة -->
                    <div class="invoice-summary">
                        <div class="summary-row">
                            <span class="label">المجموع الفرعي:</span>
                            <span class="value" id="subtotal">٠.٠٠ ريال</span>
                        </div>
                        <div class="summary-row">
                            <span class="label">الضريبة (<span id="taxRate">١٥</span>%):</span>
                            <span class="value" id="taxAmount">٠.٠٠ ريال</span>
                        </div>
                        <div class="summary-row total">
                            <span class="label">المجموع الكلي:</span>
                            <span class="value" id="totalAmount">٠.٠٠ ريال</span>
                        </div>
                    </div>

                    <!-- طرق الدفع -->
                    <div class="payment-section">
                        <div class="form-group">
                            <label>طريقة الدفع</label>
                            <div class="payment-methods">
                                <label class="payment-method">
                                    <input type="radio" name="paymentMethod" value="cash" checked>
                                    <span>نقداً</span>
                                </label>
                                <label class="payment-method">
                                    <input type="radio" name="paymentMethod" value="credit">
                                    <span>على الحساب</span>
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- أزرار الإجراءات -->
                    <div class="invoice-actions">
                        <button class="btn btn-primary btn-block" onclick="completeSale()" id="completeSaleBtn" disabled>
                            <i class="fas fa-check"></i>
                            إتمام البيع
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- نافذة تاريخ المبيعات -->
        <div id="salesHistoryModal" class="modal hidden">
            <div class="modal-content large-modal">
                <div class="modal-header">
                    <h3>تاريخ المبيعات</h3>
                    <button class="btn btn-icon" onclick="hideSalesHistoryModal()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="history-filters">
                        <div class="filter-group">
                            <input type="date" id="salesDateFrom" onchange="filterSalesHistory()">
                            <span>إلى</span>
                            <input type="date" id="salesDateTo" onchange="filterSalesHistory()">
                        </div>
                    </div>
                    <div class="table-container">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>رقم الفاتورة</th>
                                    <th>العميل</th>
                                    <th>المجموع</th>
                                    <th>طريقة الدفع</th>
                                    <th>التاريخ</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="salesHistoryTableBody">
                                <!-- سيتم ملء البيانات هنا -->
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" onclick="hideSalesHistoryModal()">إغلاق</button>
                </div>
            </div>
        </div>

        <!-- نافذة تفاصيل الفاتورة -->
        <div id="invoiceDetailsModal" class="modal hidden">
            <div class="modal-content large-modal">
                <div class="modal-header">
                    <h3>تفاصيل الفاتورة</h3>
                    <button class="btn btn-icon" onclick="hideInvoiceDetailsModal()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div id="invoiceDetailsContent">
                        <!-- سيتم ملء التفاصيل هنا -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-info" onclick="printInvoice()">
                        <i class="fas fa-print"></i>
                        طباعة
                    </button>
                    <button class="btn btn-secondary" onclick="hideInvoiceDetailsModal()">إغلاق</button>
                </div>
            </div>
        </div>
    `;
}

// متغيرات المبيعات
let cart = [];
let currentInvoice = null;
let salesProducts = [];

// تهيئة صفحة المبيعات
function initializeSales() {
    loadSalesProducts();
    loadCustomers();
    updateTaxRate();
    clearCart();
}

// تحميل المنتجات للبيع
function loadSalesProducts() {
    salesProducts = db.getData('products').filter(product => product.quantity > 0);
    displaySalesProducts(salesProducts);
}

// عرض المنتجات في شبكة البيع
function displaySalesProducts(products) {
    const grid = document.getElementById('salesProductsGrid');
    
    if (products.length === 0) {
        grid.innerHTML = '<div class="no-products">لا توجد منتجات متاحة للبيع</div>';
        return;
    }
    
    grid.innerHTML = products.map(product => `
        <div class="product-card" onclick="addToCart('${product.id}')">
            <div class="product-header">
                <h4>${product.name}</h4>
                <span class="product-price">${formatCurrency(product.price)}</span>
            </div>
            <div class="product-info">
                <span class="product-category">${product.category || 'عام'}</span>
                <span class="product-stock">متوفر: ${formatArabicNumber(product.quantity)}</span>
            </div>
            <div class="product-actions">
                <button class="btn btn-sm btn-primary">
                    <i class="fas fa-plus"></i>
                    إضافة
                </button>
            </div>
        </div>
    `).join('');
}

// تحميل العملاء
function loadCustomers() {
    const customers = db.getData('customers');
    const customerSelect = document.getElementById('customerSelect');
    
    customerSelect.innerHTML = '<option value="">اختر العميل</option>' +
        customers.map(customer => `<option value="${customer.id}">${customer.name}</option>`).join('');
}

// تحديث معدل الضريبة
function updateTaxRate() {
    const settings = db.getSettings();
    document.getElementById('taxRate').textContent = formatArabicNumber(settings.taxRate);
}

// البحث في منتجات البيع
function searchSalesProducts() {
    const searchTerm = document.getElementById('productSearchSales').value.toLowerCase();
    const filteredProducts = salesProducts.filter(product =>
        product.name.toLowerCase().includes(searchTerm) ||
        (product.category && product.category.toLowerCase().includes(searchTerm)) ||
        (product.barcode && product.barcode.includes(searchTerm))
    );
    displaySalesProducts(filteredProducts);
}

// إضافة منتج إلى السلة
function addToCart(productId) {
    const product = salesProducts.find(p => p.id === productId);
    if (!product) return;
    
    const existingItem = cart.find(item => item.productId === productId);
    
    if (existingItem) {
        if (existingItem.quantity < product.quantity) {
            existingItem.quantity++;
            existingItem.total = existingItem.quantity * existingItem.price;
        } else {
            showAlert('الكمية المطلوبة غير متوفرة في المخزون', 'warning');
            return;
        }
    } else {
        cart.push({
            productId: productId,
            name: product.name,
            price: product.price,
            quantity: 1,
            total: product.price,
            availableQuantity: product.quantity
        });
    }
    
    updateCartDisplay();
    updateInvoiceSummary();
}

// تحديث عرض السلة
function updateCartDisplay() {
    const cartItems = document.getElementById('cartItems');
    const completeSaleBtn = document.getElementById('completeSaleBtn');
    
    if (cart.length === 0) {
        cartItems.innerHTML = `
            <div class="empty-cart">
                <i class="fas fa-shopping-cart"></i>
                <p>لا توجد عناصر في الفاتورة</p>
            </div>
        `;
        completeSaleBtn.disabled = true;
        return;
    }
    
    cartItems.innerHTML = cart.map((item, index) => `
        <div class="cart-item">
            <div class="item-info">
                <h5>${item.name}</h5>
                <span class="item-price">${formatCurrency(item.price)}</span>
            </div>
            <div class="item-controls">
                <button class="btn btn-sm btn-secondary" onclick="decreaseQuantity(${index})">
                    <i class="fas fa-minus"></i>
                </button>
                <span class="quantity">${formatArabicNumber(item.quantity)}</span>
                <button class="btn btn-sm btn-secondary" onclick="increaseQuantity(${index})">
                    <i class="fas fa-plus"></i>
                </button>
            </div>
            <div class="item-total">
                <span>${formatCurrency(item.total)}</span>
                <button class="btn btn-sm btn-danger" onclick="removeFromCart(${index})">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        </div>
    `).join('');
    
    completeSaleBtn.disabled = false;
}

// زيادة الكمية
function increaseQuantity(index) {
    const item = cart[index];
    if (item.quantity < item.availableQuantity) {
        item.quantity++;
        item.total = item.quantity * item.price;
        updateCartDisplay();
        updateInvoiceSummary();
    } else {
        showAlert('الكمية المطلوبة غير متوفرة في المخزون', 'warning');
    }
}

// تقليل الكمية
function decreaseQuantity(index) {
    const item = cart[index];
    if (item.quantity > 1) {
        item.quantity--;
        item.total = item.quantity * item.price;
        updateCartDisplay();
        updateInvoiceSummary();
    }
}

// إزالة عنصر من السلة
function removeFromCart(index) {
    cart.splice(index, 1);
    updateCartDisplay();
    updateInvoiceSummary();
}

// مسح السلة
function clearCart() {
    cart = [];
    updateCartDisplay();
    updateInvoiceSummary();
    document.getElementById('customerSelect').value = '';
    updateCustomerInfo();
}

// تحديث ملخص الفاتورة
function updateInvoiceSummary() {
    const subtotal = cart.reduce((sum, item) => sum + item.total, 0);
    const settings = db.getSettings();
    const taxAmount = subtotal * (settings.taxRate / 100);
    const total = subtotal + taxAmount;
    
    document.getElementById('subtotal').textContent = formatCurrency(subtotal);
    document.getElementById('taxAmount').textContent = formatCurrency(taxAmount);
    document.getElementById('totalAmount').textContent = formatCurrency(total);
}

// تحديث معلومات العميل
function updateCustomerInfo() {
    const customerId = document.getElementById('customerSelect').value;
    const customerInfo = document.getElementById('customerInfo');

    if (!customerId) {
        customerInfo.classList.add('hidden');
        return;
    }

    const customer = db.getData('customers').find(c => c.id === customerId);
    if (customer) {
        document.getElementById('customerBalance').textContent = formatCurrency(customer.balance);
        customerInfo.classList.remove('hidden');
    }
}

// إتمام البيع
function completeSale() {
    if (cart.length === 0) {
        showAlert('لا توجد عناصر في الفاتورة', 'warning');
        return;
    }

    const customerId = document.getElementById('customerSelect').value;
    const paymentMethod = document.querySelector('input[name="paymentMethod"]:checked').value;

    // التحقق من العميل للمبيعات الآجلة
    if (paymentMethod === 'credit' && !customerId) {
        showAlert('يجب اختيار عميل للمبيعات الآجلة', 'warning');
        return;
    }

    const subtotal = cart.reduce((sum, item) => sum + item.total, 0);
    const settings = db.getSettings();
    const taxAmount = subtotal * (settings.taxRate / 100);
    const total = subtotal + taxAmount;

    // إنشاء الفاتورة
    const invoice = {
        invoiceNumber: generateInvoiceNumber(),
        customerId: customerId || null,
        customerName: customerId ? db.getData('customers').find(c => c.id === customerId).name : 'ضيف',
        items: [...cart],
        subtotal: subtotal,
        taxAmount: taxAmount,
        total: total,
        paymentMethod: paymentMethod,
        createdAt: new Date().toISOString()
    };

    try {
        // حفظ الفاتورة
        db.addItem('sales', invoice);

        // تحديث المخزون
        updateInventoryAfterSale(cart);

        // تحديث رصيد العميل للمبيعات الآجلة
        if (paymentMethod === 'credit' && customerId) {
            updateCustomerBalance(customerId, -total);
        }

        showAlert('تم إتمام البيع بنجاح', 'success');

        // عرض تفاصيل الفاتورة
        showInvoiceDetails(invoice);

        // مسح السلة
        clearCart();

        // إعادة تحميل المنتجات
        loadSalesProducts();

        // تحديث الإحصائيات
        updateStats();

    } catch (error) {
        showAlert('حدث خطأ أثناء إتمام البيع', 'error');
    }
}

// توليد رقم فاتورة
function generateInvoiceNumber() {
    const sales = db.getData('sales');
    const today = new Date();
    const dateStr = today.toISOString().split('T')[0].replace(/-/g, '');
    const dailySales = sales.filter(sale =>
        sale.createdAt.split('T')[0] === today.toISOString().split('T')[0]
    );
    const sequenceNumber = (dailySales.length + 1).toString().padStart(3, '0');
    return `INV-${dateStr}-${sequenceNumber}`;
}

// تحديث المخزون بعد البيع
function updateInventoryAfterSale(cartItems) {
    cartItems.forEach(item => {
        const product = db.getData('products').find(p => p.id === item.productId);
        if (product) {
            const newQuantity = product.quantity - item.quantity;
            db.updateItem('products', item.productId, { quantity: newQuantity });
        }
    });
}

// تحديث رصيد العميل
function updateCustomerBalance(customerId, amount) {
    const customer = db.getData('customers').find(c => c.id === customerId);
    if (customer) {
        const newBalance = customer.balance + amount;
        db.updateItem('customers', customerId, { balance: newBalance });
    }
}

// عرض تفاصيل الفاتورة
function showInvoiceDetails(invoice) {
    currentInvoice = invoice;
    const settings = db.getSettings();

    document.getElementById('invoiceDetailsContent').innerHTML = `
        <div class="invoice-details">
            <div class="invoice-header">
                <div class="company-info">
                    <h2>${settings.companyName}</h2>
                    <p>${settings.companyAddress}</p>
                    <p>هاتف: ${settings.companyPhone}</p>
                    <p>بريد إلكتروني: ${settings.companyEmail}</p>
                </div>
                <div class="invoice-info">
                    <h3>فاتورة مبيعات</h3>
                    <p><strong>رقم الفاتورة:</strong> ${invoice.invoiceNumber}</p>
                    <p><strong>التاريخ:</strong> ${formatDateTime(invoice.createdAt)}</p>
                    <p><strong>العميل:</strong> ${invoice.customerName}</p>
                    <p><strong>طريقة الدفع:</strong> ${invoice.paymentMethod === 'cash' ? 'نقداً' : 'على الحساب'}</p>
                </div>
            </div>

            <div class="invoice-items">
                <table class="invoice-table">
                    <thead>
                        <tr>
                            <th>المنتج</th>
                            <th>السعر</th>
                            <th>الكمية</th>
                            <th>المجموع</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${invoice.items.map(item => `
                            <tr>
                                <td>${item.name}</td>
                                <td>${formatCurrency(item.price)}</td>
                                <td>${formatArabicNumber(item.quantity)}</td>
                                <td>${formatCurrency(item.total)}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>

            <div class="invoice-summary">
                <div class="summary-row">
                    <span>المجموع الفرعي:</span>
                    <span>${formatCurrency(invoice.subtotal)}</span>
                </div>
                <div class="summary-row">
                    <span>الضريبة (${formatArabicNumber(settings.taxRate)}%):</span>
                    <span>${formatCurrency(invoice.taxAmount)}</span>
                </div>
                <div class="summary-row total">
                    <span>المجموع الكلي:</span>
                    <span>${formatCurrency(invoice.total)}</span>
                </div>
            </div>
        </div>
    `;

    document.getElementById('invoiceDetailsModal').classList.remove('hidden');
}

// إخفاء تفاصيل الفاتورة
function hideInvoiceDetailsModal() {
    document.getElementById('invoiceDetailsModal').classList.add('hidden');
    currentInvoice = null;
}

// طباعة الفاتورة
function printInvoice() {
    if (!currentInvoice) return;

    const content = document.getElementById('invoiceDetailsContent').innerHTML;
    printContent(content, `فاتورة رقم ${currentInvoice.invoiceNumber}`);
}

// عرض تاريخ المبيعات
function showSalesHistory() {
    loadSalesHistory();
    document.getElementById('salesHistoryModal').classList.remove('hidden');
}

// إخفاء تاريخ المبيعات
function hideSalesHistoryModal() {
    document.getElementById('salesHistoryModal').classList.add('hidden');
}

// تحميل تاريخ المبيعات
function loadSalesHistory() {
    const sales = db.getData('sales');
    displaySalesHistory(sales);
}

// عرض تاريخ المبيعات
function displaySalesHistory(sales) {
    const tbody = document.getElementById('salesHistoryTableBody');

    if (sales.length === 0) {
        tbody.innerHTML = '<tr><td colspan="6" class="text-center">لا توجد مبيعات</td></tr>';
        return;
    }

    // ترتيب المبيعات حسب التاريخ (الأحدث أولاً)
    const sortedSales = sales.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

    tbody.innerHTML = sortedSales.map(sale => `
        <tr>
            <td>${sale.invoiceNumber}</td>
            <td>${sale.customerName}</td>
            <td>${formatCurrency(sale.total)}</td>
            <td>${sale.paymentMethod === 'cash' ? 'نقداً' : 'على الحساب'}</td>
            <td>${formatDateTime(sale.createdAt)}</td>
            <td>
                <div class="action-buttons">
                    <button class="btn btn-sm btn-info" onclick="viewSaleDetails('${sale.id}')" title="عرض التفاصيل">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-sm btn-success" onclick="printSaleInvoice('${sale.id}')" title="طباعة">
                        <i class="fas fa-print"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
}

// عرض تفاصيل مبيعة
function viewSaleDetails(saleId) {
    const sale = db.getData('sales').find(s => s.id === saleId);
    if (sale) {
        hideSalesHistoryModal();
        showInvoiceDetails(sale);
    }
}

// طباعة فاتورة مبيعة
function printSaleInvoice(saleId) {
    const sale = db.getData('sales').find(s => s.id === saleId);
    if (sale) {
        const originalInvoice = currentInvoice;
        showInvoiceDetails(sale);
        setTimeout(() => {
            printInvoice();
            hideInvoiceDetailsModal();
            currentInvoice = originalInvoice;
        }, 100);
    }
}

// فلترة تاريخ المبيعات
function filterSalesHistory() {
    const dateFrom = document.getElementById('salesDateFrom').value;
    const dateTo = document.getElementById('salesDateTo').value;

    let sales = db.getData('sales');

    if (dateFrom) {
        sales = sales.filter(sale => sale.createdAt >= dateFrom + 'T00:00:00');
    }

    if (dateTo) {
        sales = sales.filter(sale => sale.createdAt <= dateTo + 'T23:59:59');
    }

    displaySalesHistory(sales);
}
