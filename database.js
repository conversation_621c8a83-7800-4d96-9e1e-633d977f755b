// إدارة قاعدة البيانات المحلية
class Database {
    constructor() {
        this.initializeDatabase();
    }

    // تهيئة قاعدة البيانات
    initializeDatabase() {
        // إنشاء الجداول الأساسية إذا لم تكن موجودة
        if (!localStorage.getItem('erp_settings')) {
            this.createDefaultSettings();
        }
        if (!localStorage.getItem('erp_products')) {
            this.createDefaultProducts();
        }
        if (!localStorage.getItem('erp_customers')) {
            this.createDefaultCustomers();
        }
        if (!localStorage.getItem('erp_suppliers')) {
            this.createDefaultSuppliers();
        }
        if (!localStorage.getItem('erp_sales')) {
            localStorage.setItem('erp_sales', JSON.stringify([]));
        }
        if (!localStorage.getItem('erp_purchases')) {
            localStorage.setItem('erp_purchases', JSON.stringify([]));
        }
        if (!localStorage.getItem('erp_payments')) {
            localStorage.setItem('erp_payments', JSON.stringify([]));
        }
        if (!localStorage.getItem('erp_categories')) {
            this.createDefaultCategories();
        }
        if (!localStorage.getItem('erp_expenses')) {
            localStorage.setItem('erp_expenses', JSON.stringify([]));
        }
    }

    // إنشاء الإعدادات الافتراضية
    createDefaultSettings() {
        const defaultSettings = {
            companyName: 'شركة نقاط البيع',
            companyAddress: 'الرياض، المملكة العربية السعودية',
            companyPhone: '٠١١-٤٥٦-٧٨٩٠',
            companyEmail: '<EMAIL>',
            companyWebsite: 'www.company.com',
            taxNumber: '١٢٣٤٥٦٧٨٩٠',
            commercialRegister: '١٠٢٠٣٠٤٠٥٠',
            taxRate: 15,
            currency: 'ريال',
            password: this.hashPassword('123'),
            theme: 'light',
            language: 'ar',
            lowStockAlert: 10,
            lowStockThreshold: 10,
            autoBackup: true,
            lowStockAlerts: true,
            soundNotifications: true,
            invoicePrefix: 'INV',
            fontSize: 'medium',
            primaryColor: '#667eea',
            compactMode: false,
            showAnimations: true,
            printSettings: {
                showLogo: true,
                showHeader: true,
                showFooter: true,
                paperSize: 'A4',
                orientation: 'portrait'
            }
        };
        localStorage.setItem('erp_settings', JSON.stringify(defaultSettings));
    }

    // إنشاء منتجات افتراضية
    createDefaultProducts() {
        const defaultProducts = [
            {
                id: this.generateId(),
                name: 'منتج تجريبي ١',
                description: 'وصف المنتج التجريبي الأول',
                price: 100,
                cost: 80,
                quantity: 50,
                category: 'عام',
                barcode: '1234567890123',
                minStock: 10,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            },
            {
                id: this.generateId(),
                name: 'منتج تجريبي ٢',
                description: 'وصف المنتج التجريبي الثاني',
                price: 200,
                cost: 150,
                quantity: 30,
                category: 'إلكترونيات',
                barcode: '1234567890124',
                minStock: 5,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            }
        ];
        localStorage.setItem('erp_products', JSON.stringify(defaultProducts));
    }

    // إنشاء عملاء افتراضيين
    createDefaultCustomers() {
        const defaultCustomers = [
            {
                id: this.generateId(),
                name: 'ضيف',
                phone: '',
                email: '',
                address: '',
                balance: 0,
                type: 'cash',
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            },
            {
                id: this.generateId(),
                name: 'عميل تجريبي',
                phone: '٠٥٠-١٢٣-٤٥٦٧',
                email: '<EMAIL>',
                address: 'الرياض',
                balance: 0,
                type: 'credit',
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            }
        ];
        localStorage.setItem('erp_customers', JSON.stringify(defaultCustomers));
    }

    // إنشاء موردين افتراضيين
    createDefaultSuppliers() {
        const defaultSuppliers = [
            {
                id: this.generateId(),
                name: 'مورد تجريبي',
                phone: '٠١١-٩٨٧-٦٥٤٣',
                email: '<EMAIL>',
                address: 'جدة',
                balance: 0,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            }
        ];
        localStorage.setItem('erp_suppliers', JSON.stringify(defaultSuppliers));
    }

    // إنشاء أصناف افتراضية
    createDefaultCategories() {
        const defaultCategories = [
            {
                id: this.generateId(),
                name: 'عام',
                description: 'منتجات عامة',
                color: '#6366f1',
                icon: 'fas fa-tag',
                active: true,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            },
            {
                id: this.generateId(),
                name: 'إلكترونيات',
                description: 'أجهزة إلكترونية ومعدات تقنية',
                color: '#3b82f6',
                icon: 'fas fa-laptop',
                active: true,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            },
            {
                id: this.generateId(),
                name: 'ملابس',
                description: 'ملابس وأزياء',
                color: '#ec4899',
                icon: 'fas fa-tshirt',
                active: true,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            }
        ];
        localStorage.setItem('erp_categories', JSON.stringify(defaultCategories));
    }

    // توليد معرف فريد
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    // تشفير كلمة المرور
    hashPassword(password) {
        // تشفير بسيط - في التطبيق الحقيقي يجب استخدام تشفير أقوى
        let hash = 0;
        for (let i = 0; i < password.length; i++) {
            const char = password.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // تحويل إلى 32bit integer
        }
        return hash.toString();
    }

    // التحقق من كلمة المرور
    verifyPassword(password) {
        const settings = this.getSettings();
        return this.hashPassword(password) === settings.password;
    }

    // الحصول على الإعدادات
    getSettings() {
        return JSON.parse(localStorage.getItem('erp_settings') || '{}');
    }

    // حفظ الإعدادات
    saveSettings(settings) {
        localStorage.setItem('erp_settings', JSON.stringify(settings));
    }

    // تحديث الإعدادات (نفس وظيفة saveSettings)
    updateSettings(settings) {
        this.saveSettings(settings);
    }

    // إعادة تعيين الإعدادات للقيم الافتراضية
    resetSettings() {
        this.createDefaultSettings();
    }

    // الحصول على البيانات من جدول معين
    getData(table) {
        return JSON.parse(localStorage.getItem(`erp_${table}`) || '[]');
    }

    // حفظ البيانات في جدول معين
    saveData(table, data) {
        localStorage.setItem(`erp_${table}`, JSON.stringify(data));
    }

    // إضافة عنصر جديد
    addItem(table, item) {
        const data = this.getData(table);
        item.id = this.generateId();
        item.createdAt = new Date().toISOString();
        item.updatedAt = new Date().toISOString();
        data.push(item);
        this.saveData(table, data);
        return item;
    }

    // تحديث عنصر
    updateItem(table, id, updates) {
        const data = this.getData(table);
        const index = data.findIndex(item => item.id === id);
        if (index !== -1) {
            data[index] = { ...data[index], ...updates, updatedAt: new Date().toISOString() };
            this.saveData(table, data);
            return data[index];
        }
        return null;
    }

    // حذف عنصر
    deleteItem(table, id) {
        const data = this.getData(table);
        const filteredData = data.filter(item => item.id !== id);
        this.saveData(table, filteredData);
        return filteredData.length < data.length;
    }

    // البحث في البيانات
    searchItems(table, searchTerm, fields = []) {
        const data = this.getData(table);
        if (!searchTerm) return data;
        
        return data.filter(item => {
            if (fields.length === 0) {
                // البحث في جميع الحقول
                return Object.values(item).some(value => 
                    value.toString().toLowerCase().includes(searchTerm.toLowerCase())
                );
            } else {
                // البحث في حقول محددة
                return fields.some(field => 
                    item[field] && item[field].toString().toLowerCase().includes(searchTerm.toLowerCase())
                );
            }
        });
    }

    // تصدير البيانات
    exportData() {
        const data = {
            settings: this.getSettings(),
            products: this.getData('products'),
            categories: this.getData('categories'),
            customers: this.getData('customers'),
            suppliers: this.getData('suppliers'),
            sales: this.getData('sales'),
            purchases: this.getData('purchases'),
            payments: this.getData('payments'),
            expenses: this.getData('expenses'),
            exportDate: new Date().toISOString()
        };
        return JSON.stringify(data, null, 2);
    }

    // استيراد البيانات
    importData(jsonData) {
        try {
            const data = JSON.parse(jsonData);
            
            // التحقق من صحة البيانات
            if (!data.exportDate) {
                throw new Error('ملف البيانات غير صحيح');
            }

            // استيراد البيانات
            if (data.settings) localStorage.setItem('erp_settings', JSON.stringify(data.settings));
            if (data.products) localStorage.setItem('erp_products', JSON.stringify(data.products));
            if (data.categories) localStorage.setItem('erp_categories', JSON.stringify(data.categories));
            if (data.customers) localStorage.setItem('erp_customers', JSON.stringify(data.customers));
            if (data.suppliers) localStorage.setItem('erp_suppliers', JSON.stringify(data.suppliers));
            if (data.sales) localStorage.setItem('erp_sales', JSON.stringify(data.sales));
            if (data.purchases) localStorage.setItem('erp_purchases', JSON.stringify(data.purchases));
            if (data.payments) localStorage.setItem('erp_payments', JSON.stringify(data.payments));
            if (data.expenses) localStorage.setItem('erp_expenses', JSON.stringify(data.expenses));

            return true;
        } catch (error) {
            console.error('خطأ في استيراد البيانات:', error);
            return false;
        }
    }

    // مسح جميع البيانات
    clearAllData() {
        const keys = ['erp_settings', 'erp_products', 'erp_categories', 'erp_customers', 'erp_suppliers',
                     'erp_sales', 'erp_purchases', 'erp_payments', 'erp_expenses'];
        keys.forEach(key => localStorage.removeItem(key));
        this.initializeDatabase();
    }

    // تحويل الأرقام إلى عربية
    toArabicNumbers(str) {
        const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
        return str.toString().replace(/[0-9]/g, (w) => arabicNumbers[+w]);
    }

    // تحويل الأرقام إلى إنجليزية
    toEnglishNumbers(str) {
        const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
        let result = str.toString();
        for (let i = 0; i < arabicNumbers.length; i++) {
            result = result.replace(new RegExp(arabicNumbers[i], 'g'), i.toString());
        }
        return result;
    }

    // تنسيق العملة
    formatCurrency(amount) {
        const settings = this.getSettings();
        const formattedAmount = new Intl.NumberFormat('ar-SA').format(amount);
        return `${this.toArabicNumbers(formattedAmount)} ${settings.currency}`;
    }

    // تنسيق التاريخ
    formatDate(date) {
        const d = new Date(date);
        return this.toArabicNumbers(d.toLocaleDateString('ar-SA'));
    }

    // تنسيق الوقت
    formatDateTime(date) {
        const d = new Date(date);
        return this.toArabicNumbers(d.toLocaleString('ar-SA'));
    }
}

// إنشاء مثيل من قاعدة البيانات
const db = new Database();
