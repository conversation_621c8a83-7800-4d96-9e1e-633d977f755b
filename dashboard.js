// لوحة المعلومات الرئيسية
function getDashboardHTML() {
    return `
        <div class="dashboard-container">
            <!-- الملخص العام -->
            <div class="summary-section">
                <h2 class="section-title">الملخص العام</h2>
                <div class="stats-grid">
                    <div class="stat-card primary">
                        <div class="stat-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="totalSales">٠.٠٠ ريال</h3>
                            <p>إجمالي المبيعات</p>
                        </div>
                    </div>
                    
                    <div class="stat-card success">
                        <div class="stat-icon">
                            <i class="fas fa-box"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="totalProducts">٠</h3>
                            <p>عدد المنتجات</p>
                        </div>
                    </div>
                    
                    <div class="stat-card info">
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="totalCustomers">٠</h3>
                            <p>عدد العملاء</p>
                        </div>
                    </div>
                    
                    <div class="stat-card warning">
                        <div class="stat-icon">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="lowStockItems">٠</h3>
                            <p>منتجات منخفضة المخزون</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- لوحة المعلومات - الثقل أو الرئيسة -->
            <div class="main-dashboard">
                <div class="dashboard-row">
                    <!-- المبيعات -->
                    <div class="dashboard-card">
                        <div class="card-header">
                            <h3>المبيعات</h3>
                            <div class="card-actions">
                                <button class="btn btn-sm btn-primary" onclick="loadPage('sales')">
                                    <i class="fas fa-plus"></i>
                                    بيع جديد
                                </button>
                            </div>
                        </div>
                        <div class="card-content">
                            <div class="sales-stats">
                                <div class="sales-item">
                                    <span class="label">مبيعات اليوم:</span>
                                    <span class="value" id="todaySales">٠.٠٠ ريال</span>
                                </div>
                                <div class="sales-item">
                                    <span class="label">مبيعات الشهر:</span>
                                    <span class="value" id="monthSales">٠.٠٠ ريال</span>
                                </div>
                                <div class="sales-item">
                                    <span class="label">عدد الفواتير اليوم:</span>
                                    <span class="value" id="todayInvoices">٠</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- المخزون -->
                    <div class="dashboard-card">
                        <div class="card-header">
                            <h3>المخزون</h3>
                            <div class="card-actions">
                                <button class="btn btn-sm btn-success" onclick="loadPage('products')">
                                    <i class="fas fa-eye"></i>
                                    عرض المنتجات
                                </button>
                            </div>
                        </div>
                        <div class="card-content">
                            <div class="inventory-stats">
                                <div class="inventory-item">
                                    <span class="label">إجمالي المنتجات:</span>
                                    <span class="value" id="inventoryTotal">٠</span>
                                </div>
                                <div class="inventory-item">
                                    <span class="label">قيمة المخزون:</span>
                                    <span class="value" id="inventoryValue">٠.٠٠ ريال</span>
                                </div>
                                <div class="inventory-item warning">
                                    <span class="label">تحتاج إعادة طلب:</span>
                                    <span class="value" id="reorderItems">٠</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- المشتريات -->
                <div class="dashboard-row">
                    <div class="dashboard-card">
                        <div class="card-header">
                            <h3>المشتريات</h3>
                            <div class="card-actions">
                                <button class="btn btn-sm btn-info" onclick="loadPage('purchases')">
                                    <i class="fas fa-plus"></i>
                                    شراء جديد
                                </button>
                            </div>
                        </div>
                        <div class="card-content">
                            <div class="purchases-stats">
                                <div class="purchase-item">
                                    <span class="label">مشتريات اليوم:</span>
                                    <span class="value" id="todayPurchases">٠.٠٠ ريال</span>
                                </div>
                                <div class="purchase-item">
                                    <span class="label">مشتريات الشهر:</span>
                                    <span class="value" id="monthPurchases">٠.٠٠ ريال</span>
                                </div>
                                <div class="purchase-item">
                                    <span class="label">عدد الفواتير:</span>
                                    <span class="value" id="purchaseInvoices">٠</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- الديون -->
                    <div class="dashboard-card">
                        <div class="card-header">
                            <h3>الديون</h3>
                            <div class="card-actions">
                                <button class="btn btn-sm btn-warning" onclick="loadPage('debts')">
                                    <i class="fas fa-eye"></i>
                                    إدارة الديون
                                </button>
                            </div>
                        </div>
                        <div class="card-content">
                            <div class="debts-stats">
                                <div class="debt-item">
                                    <span class="label">إجمالي الديون:</span>
                                    <span class="value" id="totalDebts">٠.٠٠ ريال</span>
                                </div>
                                <div class="debt-item">
                                    <span class="label">عملاء مدينون:</span>
                                    <span class="value" id="debtorCustomers">٠</span>
                                </div>
                                <div class="debt-item">
                                    <span class="label">ديون متأخرة:</span>
                                    <span class="value" id="overdueDebts">٠.٠٠ ريال</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- التنبيهات -->
            <div class="alerts-section">
                <h3>التنبيهات</h3>
                <div id="alertsList" class="alerts-list">
                    <!-- سيتم ملء التنبيهات هنا -->
                </div>
            </div>

            <!-- آخر العمليات -->
            <div class="recent-activities">
                <h3>آخر العمليات</h3>
                <div id="recentActivities" class="activities-list">
                    <!-- سيتم ملء العمليات الأخيرة هنا -->
                </div>
            </div>
        </div>
    `;
}

// تهيئة لوحة المعلومات
function initializeDashboard() {
    updateDashboardStats();
    loadAlerts();
    loadRecentActivities();
}

// تحديث إحصائيات لوحة المعلومات
function updateDashboardStats() {
    const products = db.getData('products');
    const customers = db.getData('customers');
    const sales = db.getData('sales');
    const purchases = db.getData('purchases');
    
    // إحصائيات عامة
    const totalProducts = products.length;
    const totalCustomers = customers.length - 1; // استثناء عميل "ضيف"
    const lowStockItems = products.filter(p => p.quantity <= p.minStock).length;
    
    // حساب إجمالي المبيعات
    const totalSalesAmount = sales.reduce((sum, sale) => sum + sale.total, 0);
    
    // مبيعات اليوم
    const today = new Date().toDateString();
    const todaySales = sales
        .filter(sale => new Date(sale.createdAt).toDateString() === today)
        .reduce((sum, sale) => sum + sale.total, 0);
    
    // مبيعات الشهر
    const currentMonth = new Date().getMonth();
    const currentYear = new Date().getFullYear();
    const monthSales = sales
        .filter(sale => {
            const saleDate = new Date(sale.createdAt);
            return saleDate.getMonth() === currentMonth && saleDate.getFullYear() === currentYear;
        })
        .reduce((sum, sale) => sum + sale.total, 0);
    
    // عدد فواتير اليوم
    const todayInvoices = sales.filter(sale => 
        new Date(sale.createdAt).toDateString() === today
    ).length;
    
    // مشتريات اليوم والشهر
    const todayPurchases = purchases
        .filter(purchase => new Date(purchase.createdAt).toDateString() === today)
        .reduce((sum, purchase) => sum + purchase.total, 0);
    
    const monthPurchases = purchases
        .filter(purchase => {
            const purchaseDate = new Date(purchase.createdAt);
            return purchaseDate.getMonth() === currentMonth && purchaseDate.getFullYear() === currentYear;
        })
        .reduce((sum, purchase) => sum + purchase.total, 0);
    
    // عدد فواتير المشتريات
    const purchaseInvoices = purchases.length;
    
    // قيمة المخزون
    const inventoryValue = products.reduce((sum, product) => 
        sum + (product.quantity * product.cost), 0);
    
    // المنتجات التي تحتاج إعادة طلب
    const reorderItems = products.filter(p => p.quantity <= p.minStock).length;
    
    // حساب الديون
    const totalDebts = customers
        .filter(customer => customer.balance < 0)
        .reduce((sum, customer) => sum + Math.abs(customer.balance), 0);
    
    const debtorCustomers = customers.filter(customer => customer.balance < 0).length;
    
    // تحديث العناصر في الواجهة
    document.getElementById('totalSales').textContent = formatCurrency(totalSalesAmount);
    document.getElementById('totalProducts').textContent = formatArabicNumber(totalProducts);
    document.getElementById('totalCustomers').textContent = formatArabicNumber(totalCustomers);
    document.getElementById('lowStockItems').textContent = formatArabicNumber(lowStockItems);
    
    document.getElementById('todaySales').textContent = formatCurrency(todaySales);
    document.getElementById('monthSales').textContent = formatCurrency(monthSales);
    document.getElementById('todayInvoices').textContent = formatArabicNumber(todayInvoices);
    
    document.getElementById('inventoryTotal').textContent = formatArabicNumber(totalProducts);
    document.getElementById('inventoryValue').textContent = formatCurrency(inventoryValue);
    document.getElementById('reorderItems').textContent = formatArabicNumber(reorderItems);
    
    document.getElementById('todayPurchases').textContent = formatCurrency(todayPurchases);
    document.getElementById('monthPurchases').textContent = formatCurrency(monthPurchases);
    document.getElementById('purchaseInvoices').textContent = formatArabicNumber(purchaseInvoices);
    
    document.getElementById('totalDebts').textContent = formatCurrency(totalDebts);
    document.getElementById('debtorCustomers').textContent = formatArabicNumber(debtorCustomers);
    document.getElementById('overdueDebts').textContent = formatCurrency(0); // يمكن تطويرها لاحقاً
}

// تحميل التنبيهات
function loadAlerts() {
    const alertsList = document.getElementById('alertsList');
    const products = db.getData('products');
    const customers = db.getData('customers');
    
    let alerts = [];
    
    // تنبيهات المخزون المنخفض
    const lowStockProducts = products.filter(p => p.quantity <= p.minStock);
    lowStockProducts.forEach(product => {
        alerts.push({
            type: 'warning',
            icon: 'fas fa-exclamation-triangle',
            message: `المنتج "${product.name}" منخفض المخزون (${formatArabicNumber(product.quantity)} متبقي)`,
            action: () => loadPage('products')
        });
    });
    
    // تنبيهات الديون
    const debtorCustomers = customers.filter(customer => customer.balance < 0);
    if (debtorCustomers.length > 0) {
        alerts.push({
            type: 'info',
            icon: 'fas fa-credit-card',
            message: `يوجد ${formatArabicNumber(debtorCustomers.length)} عميل مدين`,
            action: () => loadPage('debts')
        });
    }
    
    // عرض التنبيهات
    if (alerts.length === 0) {
        alertsList.innerHTML = '<div class="no-alerts">لا توجد تنبيهات</div>';
    } else {
        alertsList.innerHTML = alerts.map(alert => `
            <div class="alert-item ${alert.type}" onclick="alert.action && alert.action()">
                <i class="${alert.icon}"></i>
                <span>${alert.message}</span>
            </div>
        `).join('');
    }
}

// تحميل آخر العمليات
function loadRecentActivities() {
    const activitiesList = document.getElementById('recentActivities');
    const sales = db.getData('sales');
    const purchases = db.getData('purchases');
    
    // دمج المبيعات والمشتريات وترتيبها حسب التاريخ
    let activities = [];
    
    // إضافة المبيعات
    sales.slice(-5).forEach(sale => {
        activities.push({
            type: 'sale',
            icon: 'fas fa-shopping-cart',
            message: `بيع بقيمة ${formatCurrency(sale.total)}`,
            date: sale.createdAt,
            color: 'success'
        });
    });
    
    // إضافة المشتريات
    purchases.slice(-5).forEach(purchase => {
        activities.push({
            type: 'purchase',
            icon: 'fas fa-shopping-bag',
            message: `شراء بقيمة ${formatCurrency(purchase.total)}`,
            date: purchase.createdAt,
            color: 'info'
        });
    });
    
    // ترتيب حسب التاريخ (الأحدث أولاً)
    activities.sort((a, b) => new Date(b.date) - new Date(a.date));
    activities = activities.slice(0, 10); // أحدث 10 عمليات
    
    // عرض العمليات
    if (activities.length === 0) {
        activitiesList.innerHTML = '<div class="no-activities">لا توجد عمليات حديثة</div>';
    } else {
        activitiesList.innerHTML = activities.map(activity => `
            <div class="activity-item">
                <div class="activity-icon ${activity.color}">
                    <i class="${activity.icon}"></i>
                </div>
                <div class="activity-content">
                    <div class="activity-message">${activity.message}</div>
                    <div class="activity-date">${formatDateTime(activity.date)}</div>
                </div>
            </div>
        `).join('');
    }
}
