// إدارة المصروفات

// HTML صفحة المصروفات
function getExpensesHTML() {
    return `
        <div class="page-header">
            <div class="page-title">
                <h2>إدارة المصروفات</h2>
            </div>
            <div class="page-actions">
                <button class="btn btn-primary" onclick="showAddExpenseModal()">
                    <i class="fas fa-plus"></i>
                    إضافة مصروف جديد
                </button>
            </div>
        </div>

        <!-- إحصائيات المصروفات -->
        <div class="stats-grid">
            <div class="stat-card danger">
                <div class="stat-icon">
                    <i class="fas fa-money-bill-wave"></i>
                </div>
                <div class="stat-content">
                    <h3 id="totalExpenses">0.00 ر.س</h3>
                    <p>إجمالي المصروفات</p>
                </div>
            </div>
            <div class="stat-card warning">
                <div class="stat-icon">
                    <i class="fas fa-calendar-day"></i>
                </div>
                <div class="stat-content">
                    <h3 id="todayExpenses">0.00 ر.س</h3>
                    <p>مصروفات اليوم</p>
                </div>
            </div>
            <div class="stat-card info">
                <div class="stat-icon">
                    <i class="fas fa-calendar-week"></i>
                </div>
                <div class="stat-content">
                    <h3 id="weekExpenses">0.00 ر.س</h3>
                    <p>مصروفات الأسبوع</p>
                </div>
            </div>
            <div class="stat-card primary">
                <div class="stat-icon">
                    <i class="fas fa-calendar-alt"></i>
                </div>
                <div class="stat-content">
                    <h3 id="monthExpenses">0.00 ر.س</h3>
                    <p>مصروفات الشهر</p>
                </div>
            </div>
        </div>

        <!-- شريط البحث والفلترة -->
        <div class="search-filter-bar">
            <div class="search-group">
                <div class="search-input">
                    <input type="text" id="expenseSearch" placeholder="البحث في المصروفات...">
                    <i class="fas fa-search"></i>
                </div>
            </div>
            <div class="filter-group">
                <select id="expenseCategoryFilter">
                    <option value="">جميع الفئات</option>
                    <option value="office">مكتبية</option>
                    <option value="transport">مواصلات</option>
                    <option value="utilities">مرافق</option>
                    <option value="maintenance">صيانة</option>
                    <option value="marketing">تسويق</option>
                    <option value="other">أخرى</option>
                </select>
                <input type="date" id="expenseDateFilter" title="فلترة حسب التاريخ">
            </div>
        </div>

        <!-- جدول المصروفات -->
        <div class="table-container">
            <table class="data-table">
                <thead>
                    <tr>
                        <th>الوصف</th>
                        <th>الفئة</th>
                        <th>المبلغ</th>
                        <th>التاريخ</th>
                        <th>طريقة الدفع</th>
                        <th>الملاحظات</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody id="expensesTableBody">
                    <!-- سيتم ملء البيانات هنا -->
                </tbody>
            </table>
        </div>

        <!-- نافذة إضافة/تعديل مصروف -->
        <div id="expenseModal" class="modal hidden">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 id="expenseModalTitle">إضافة مصروف جديد</h3>
                </div>
                <div class="modal-body">
                    <form id="expenseForm">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="expenseDescription">وصف المصروف *</label>
                                <input type="text" id="expenseDescription" required placeholder="مثال: فاتورة كهرباء">
                            </div>
                            
                            <div class="form-group">
                                <label for="expenseAmount">المبلغ *</label>
                                <input type="number" id="expenseAmount" step="0.01" min="0" required placeholder="0.00">
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="expenseCategory">الفئة *</label>
                                <select id="expenseCategory" required>
                                    <option value="">اختر الفئة</option>
                                    <option value="office">مكتبية</option>
                                    <option value="transport">مواصلات</option>
                                    <option value="utilities">مرافق</option>
                                    <option value="maintenance">صيانة</option>
                                    <option value="marketing">تسويق</option>
                                    <option value="salary">رواتب</option>
                                    <option value="rent">إيجار</option>
                                    <option value="insurance">تأمين</option>
                                    <option value="other">أخرى</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label for="expenseDate">التاريخ *</label>
                                <input type="date" id="expenseDate" required>
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="expensePaymentMethod">طريقة الدفع</label>
                                <select id="expensePaymentMethod">
                                    <option value="cash">نقدي</option>
                                    <option value="card">بطاقة</option>
                                    <option value="bank">تحويل بنكي</option>
                                    <option value="check">شيك</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label for="expenseReference">رقم المرجع</label>
                                <input type="text" id="expenseReference" placeholder="رقم الفاتورة أو المرجع">
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="expenseNotes">ملاحظات</label>
                            <textarea id="expenseNotes" rows="3" placeholder="ملاحظات إضافية..."></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" onclick="hideExpenseModal()">إلغاء</button>
                    <button class="btn btn-primary" onclick="saveExpense()">حفظ</button>
                </div>
            </div>
        </div>

        <!-- نافذة تفاصيل المصروف -->
        <div id="expenseDetailsModal" class="modal hidden">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>تفاصيل المصروف</h3>
                </div>
                <div class="modal-body">
                    <div id="expenseDetailsContent">
                        <!-- سيتم ملء التفاصيل هنا -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" onclick="hideExpenseDetailsModal()">إغلاق</button>
                    <button class="btn btn-primary" onclick="printExpenseDetails()">طباعة</button>
                </div>
            </div>
        </div>
    `;
}

// متغيرات المصروفات
let expensesData = [];
let currentExpense = null;

// تهيئة صفحة المصروفات
function initializeExpenses() {
    loadExpenses();
    updateExpensesStats();
    setupExpensesEventListeners();
    
    // تعيين التاريخ الحالي كافتراضي
    document.getElementById('expenseDate').value = new Date().toISOString().split('T')[0];
}

// إعداد مستمعي الأحداث
function setupExpensesEventListeners() {
    // البحث
    document.getElementById('expenseSearch').addEventListener('input', function() {
        filterExpenses();
    });
    
    // فلترة الفئة
    document.getElementById('expenseCategoryFilter').addEventListener('change', function() {
        filterExpenses();
    });
    
    // فلترة التاريخ
    document.getElementById('expenseDateFilter').addEventListener('change', function() {
        filterExpenses();
    });
}

// تحميل المصروفات
function loadExpenses() {
    expensesData = db.getData('expenses') || [];
    displayExpenses(expensesData);
}

// عرض المصروفات في الجدول
function displayExpenses(expenses) {
    const tbody = document.getElementById('expensesTableBody');
    
    if (expenses.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="7" class="text-center">لا توجد مصروفات مسجلة</td>
            </tr>
        `;
        return;
    }
    
    tbody.innerHTML = expenses.map(expense => {
        const categoryNames = {
            'office': 'مكتبية',
            'transport': 'مواصلات',
            'utilities': 'مرافق',
            'maintenance': 'صيانة',
            'marketing': 'تسويق',
            'salary': 'رواتب',
            'rent': 'إيجار',
            'insurance': 'تأمين',
            'other': 'أخرى'
        };
        
        const paymentMethods = {
            'cash': 'نقدي',
            'card': 'بطاقة',
            'bank': 'تحويل بنكي',
            'check': 'شيك'
        };
        
        return `
            <tr>
                <td>
                    <div class="expense-description">
                        <strong>${expense.description}</strong>
                        ${expense.reference ? `<br><small>المرجع: ${expense.reference}</small>` : ''}
                    </div>
                </td>
                <td>
                    <span class="category-badge category-${expense.category}">
                        ${categoryNames[expense.category] || expense.category}
                    </span>
                </td>
                <td class="amount-cell">
                    <strong>${db.formatCurrency(expense.amount)}</strong>
                </td>
                <td>${db.formatDate(expense.date)}</td>
                <td>${paymentMethods[expense.paymentMethod] || expense.paymentMethod}</td>
                <td>${expense.notes ? expense.notes.substring(0, 50) + (expense.notes.length > 50 ? '...' : '') : '-'}</td>
                <td>
                    <div class="action-buttons">
                        <button class="btn btn-sm btn-info" onclick="showExpenseDetails('${expense.id}')" title="التفاصيل">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-warning" onclick="editExpense('${expense.id}')" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="deleteExpense('${expense.id}')" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }).join('');
}

// تحديث إحصائيات المصروفات
function updateExpensesStats() {
    const today = new Date();
    const todayStr = today.toISOString().split('T')[0];

    // حساب بداية الأسبوع (الأحد)
    const weekStart = new Date(today);
    weekStart.setDate(today.getDate() - today.getDay());
    const weekStartStr = weekStart.toISOString().split('T')[0];

    // حساب بداية الشهر
    const monthStart = new Date(today.getFullYear(), today.getMonth(), 1);
    const monthStartStr = monthStart.toISOString().split('T')[0];

    const total = expensesData.reduce((sum, expense) => sum + expense.amount, 0);
    const todayTotal = expensesData
        .filter(expense => expense.date === todayStr)
        .reduce((sum, expense) => sum + expense.amount, 0);
    const weekTotal = expensesData
        .filter(expense => expense.date >= weekStartStr)
        .reduce((sum, expense) => sum + expense.amount, 0);
    const monthTotal = expensesData
        .filter(expense => expense.date >= monthStartStr)
        .reduce((sum, expense) => sum + expense.amount, 0);

    document.getElementById('totalExpenses').textContent = db.formatCurrency(total);
    document.getElementById('todayExpenses').textContent = db.formatCurrency(todayTotal);
    document.getElementById('weekExpenses').textContent = db.formatCurrency(weekTotal);
    document.getElementById('monthExpenses').textContent = db.formatCurrency(monthTotal);
}

// فلترة المصروفات
function filterExpenses() {
    const searchTerm = document.getElementById('expenseSearch').value.toLowerCase();
    const categoryFilter = document.getElementById('expenseCategoryFilter').value;
    const dateFilter = document.getElementById('expenseDateFilter').value;

    let filteredExpenses = expensesData;

    // فلترة البحث
    if (searchTerm) {
        filteredExpenses = filteredExpenses.filter(expense =>
            expense.description.toLowerCase().includes(searchTerm) ||
            (expense.notes && expense.notes.toLowerCase().includes(searchTerm)) ||
            (expense.reference && expense.reference.toLowerCase().includes(searchTerm))
        );
    }

    // فلترة الفئة
    if (categoryFilter) {
        filteredExpenses = filteredExpenses.filter(expense => expense.category === categoryFilter);
    }

    // فلترة التاريخ
    if (dateFilter) {
        filteredExpenses = filteredExpenses.filter(expense => expense.date === dateFilter);
    }

    displayExpenses(filteredExpenses);
}

// عرض نافذة إضافة مصروف
function showAddExpenseModal() {
    currentExpense = null;
    document.getElementById('expenseModalTitle').textContent = 'إضافة مصروف جديد';
    document.getElementById('expenseForm').reset();
    document.getElementById('expenseDate').value = new Date().toISOString().split('T')[0];
    document.getElementById('expensePaymentMethod').value = 'cash';
    document.getElementById('expenseModal').classList.remove('hidden');
}

// إخفاء نافذة المصروف
function hideExpenseModal() {
    document.getElementById('expenseModal').classList.add('hidden');
    currentExpense = null;
}

// تعديل مصروف
function editExpense(expenseId) {
    const expense = expensesData.find(e => e.id === expenseId);
    if (!expense) return;

    currentExpense = expense;
    document.getElementById('expenseModalTitle').textContent = 'تعديل المصروف';
    document.getElementById('expenseDescription').value = expense.description;
    document.getElementById('expenseAmount').value = expense.amount;
    document.getElementById('expenseCategory').value = expense.category;
    document.getElementById('expenseDate').value = expense.date;
    document.getElementById('expensePaymentMethod').value = expense.paymentMethod || 'cash';
    document.getElementById('expenseReference').value = expense.reference || '';
    document.getElementById('expenseNotes').value = expense.notes || '';

    document.getElementById('expenseModal').classList.remove('hidden');
}

// حفظ المصروف
function saveExpense() {
    const description = document.getElementById('expenseDescription').value.trim();
    const amount = parseFloat(document.getElementById('expenseAmount').value);
    const category = document.getElementById('expenseCategory').value;
    const date = document.getElementById('expenseDate').value;
    const paymentMethod = document.getElementById('expensePaymentMethod').value;
    const reference = document.getElementById('expenseReference').value.trim();
    const notes = document.getElementById('expenseNotes').value.trim();

    if (!description) {
        showAlert('يرجى إدخال وصف المصروف', 'error');
        return;
    }

    if (!amount || amount <= 0) {
        showAlert('يرجى إدخال مبلغ صحيح', 'error');
        return;
    }

    if (!category) {
        showAlert('يرجى اختيار فئة المصروف', 'error');
        return;
    }

    if (!date) {
        showAlert('يرجى اختيار تاريخ المصروف', 'error');
        return;
    }

    const expenseData = {
        description,
        amount,
        category,
        date,
        paymentMethod,
        reference,
        notes
    };

    try {
        if (currentExpense) {
            // تحديث مصروف موجود
            db.updateItem('expenses', currentExpense.id, expenseData);
            showAlert('تم تحديث المصروف بنجاح', 'success');
        } else {
            // إضافة مصروف جديد
            db.addItem('expenses', expenseData);
            showAlert('تم إضافة المصروف بنجاح', 'success');
        }

        hideExpenseModal();
        loadExpenses();
        updateExpensesStats();

    } catch (error) {
        showAlert('حدث خطأ أثناء حفظ المصروف', 'error');
    }
}

// حذف مصروف
function deleteExpense(expenseId) {
    const expense = expensesData.find(e => e.id === expenseId);
    if (!expense) return;

    showConfirm(`هل أنت متأكد من حذف المصروف "${expense.description}"؟`, function() {
        try {
            db.deleteItem('expenses', expenseId);
            showAlert('تم حذف المصروف بنجاح', 'success');
            loadExpenses();
            updateExpensesStats();
        } catch (error) {
            showAlert('حدث خطأ أثناء حذف المصروف', 'error');
        }
    });
}

// عرض تفاصيل المصروف
function showExpenseDetails(expenseId) {
    const expense = expensesData.find(e => e.id === expenseId);
    if (!expense) return;

    const categoryNames = {
        'office': 'مكتبية',
        'transport': 'مواصلات',
        'utilities': 'مرافق',
        'maintenance': 'صيانة',
        'marketing': 'تسويق',
        'salary': 'رواتب',
        'rent': 'إيجار',
        'insurance': 'تأمين',
        'other': 'أخرى'
    };

    const paymentMethods = {
        'cash': 'نقدي',
        'card': 'بطاقة',
        'bank': 'تحويل بنكي',
        'check': 'شيك'
    };

    const detailsHTML = `
        <div class="expense-details">
            <div class="expense-header">
                <div class="expense-icon">
                    <i class="fas fa-money-bill-wave"></i>
                </div>
                <div class="expense-info">
                    <h3>${expense.description}</h3>
                    <p class="expense-amount">${db.formatCurrency(expense.amount)}</p>
                </div>
            </div>

            <div class="details-grid">
                <div class="detail-row">
                    <span class="detail-label">الفئة:</span>
                    <span class="detail-value">
                        <span class="category-badge category-${expense.category}">
                            ${categoryNames[expense.category] || expense.category}
                        </span>
                    </span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">التاريخ:</span>
                    <span class="detail-value">${db.formatDate(expense.date)}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">طريقة الدفع:</span>
                    <span class="detail-value">${paymentMethods[expense.paymentMethod] || expense.paymentMethod}</span>
                </div>
                ${expense.reference ? `
                    <div class="detail-row">
                        <span class="detail-label">رقم المرجع:</span>
                        <span class="detail-value">${expense.reference}</span>
                    </div>
                ` : ''}
                <div class="detail-row">
                    <span class="detail-label">تاريخ الإنشاء:</span>
                    <span class="detail-value">${db.formatDateTime(expense.createdAt)}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">آخر تحديث:</span>
                    <span class="detail-value">${db.formatDateTime(expense.updatedAt)}</span>
                </div>
                ${expense.notes ? `
                    <div class="detail-row">
                        <span class="detail-label">الملاحظات:</span>
                        <span class="detail-value">${expense.notes}</span>
                    </div>
                ` : ''}
            </div>
        </div>
    `;

    document.getElementById('expenseDetailsContent').innerHTML = detailsHTML;
    document.getElementById('expenseDetailsModal').classList.remove('hidden');
}

// إخفاء نافذة تفاصيل المصروف
function hideExpenseDetailsModal() {
    document.getElementById('expenseDetailsModal').classList.add('hidden');
}

// طباعة تفاصيل المصروف
function printExpenseDetails() {
    const content = document.getElementById('expenseDetailsContent').innerHTML;
    printContent(content, 'تفاصيل المصروف');
}
