// نظام المشتريات
function getPurchasesHTML() {
    return `
        <div class="purchases-container">
            <div class="page-header">
                <h2 class="page-title">نظام المشتريات</h2>
                <div class="page-actions">
                    <button class="btn btn-primary" onclick="showAddPurchaseModal()">
                        <i class="fas fa-plus"></i>
                        إضافة فاتورة شراء
                    </button>
                    <button class="btn btn-success" onclick="showPurchasesHistory()">
                        <i class="fas fa-history"></i>
                        تاريخ المشتريات
                    </button>
                </div>
            </div>

            <!-- شريط البحث والفلترة -->
            <div class="search-filter-bar">
                <div class="search-group">
                    <div class="input-group">
                        <input type="text" id="purchaseSearch" placeholder="البحث في المشتريات..." onkeyup="searchPurchases()">
                        <i class="fas fa-search"></i>
                    </div>
                </div>
                <div class="filter-group">
                    <select id="supplierFilter" onchange="filterPurchases()">
                        <option value="">جميع الموردين</option>
                    </select>
                    <input type="date" id="dateFromFilter" onchange="filterPurchases()">
                    <input type="date" id="dateToFilter" onchange="filterPurchases()">
                </div>
            </div>

            <!-- جدول المشتريات -->
            <div class="table-container">
                <table class="data-table" id="purchasesTable">
                    <thead>
                        <tr>
                            <th>رقم الفاتورة</th>
                            <th>المورد</th>
                            <th>المجموع</th>
                            <th>طريقة الدفع</th>
                            <th>التاريخ</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="purchasesTableBody">
                        <!-- سيتم ملء البيانات هنا -->
                    </tbody>
                </table>
            </div>

            <!-- إحصائيات المشتريات -->
            <div class="purchases-stats">
                <div class="stat-item">
                    <span class="label">إجمالي المشتريات:</span>
                    <span class="value" id="totalPurchasesAmount">٠.٠٠ ريال</span>
                </div>
                <div class="stat-item">
                    <span class="label">مشتريات اليوم:</span>
                    <span class="value" id="todayPurchasesAmount">٠.٠٠ ريال</span>
                </div>
                <div class="stat-item">
                    <span class="label">عدد الفواتير:</span>
                    <span class="value" id="totalPurchasesCount">٠</span>
                </div>
            </div>
        </div>

        <!-- نافذة إضافة فاتورة شراء -->
        <div id="purchaseModal" class="modal hidden">
            <div class="modal-content large-modal">
                <div class="modal-header">
                    <h3>إضافة فاتورة شراء</h3>
                    <button class="btn btn-icon" onclick="hidePurchaseModal()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="purchaseForm">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="purchaseInvoiceNumber">رقم الفاتورة</label>
                                <input type="text" id="purchaseInvoiceNumber" placeholder="اختياري">
                            </div>
                            <div class="form-group">
                                <label for="purchaseSupplier">المورد *</label>
                                <select id="purchaseSupplier" required>
                                    <option value="">اختر المورد</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label>عناصر الفاتورة</label>
                            <div class="purchase-items-section">
                                <div class="items-header">
                                    <button type="button" class="btn btn-sm btn-primary" onclick="addPurchaseItem()">
                                        <i class="fas fa-plus"></i>
                                        إضافة عنصر
                                    </button>
                                </div>
                                <div id="purchaseItemsList" class="items-list">
                                    <!-- سيتم إضافة العناصر هنا -->
                                </div>
                            </div>
                        </div>
                        
                        <div class="purchase-summary">
                            <div class="summary-row">
                                <span class="label">المجموع الفرعي:</span>
                                <span class="value" id="purchaseSubtotal">٠.٠٠ ريال</span>
                            </div>
                            <div class="summary-row">
                                <span class="label">الضريبة (<span id="purchaseTaxRate">١٥</span>%):</span>
                                <span class="value" id="purchaseTaxAmount">٠.٠٠ ريال</span>
                            </div>
                            <div class="summary-row total">
                                <span class="label">المجموع الكلي:</span>
                                <span class="value" id="purchaseTotal">٠.٠٠ ريال</span>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label>طريقة الدفع</label>
                            <div class="payment-methods">
                                <label class="payment-method">
                                    <input type="radio" name="purchasePaymentMethod" value="cash" checked>
                                    <span>نقداً</span>
                                </label>
                                <label class="payment-method">
                                    <input type="radio" name="purchasePaymentMethod" value="credit">
                                    <span>على الحساب</span>
                                </label>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="purchaseNotes">ملاحظات</label>
                            <textarea id="purchaseNotes" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" onclick="hidePurchaseModal()">إلغاء</button>
                    <button class="btn btn-primary" onclick="savePurchase()">حفظ الفاتورة</button>
                </div>
            </div>
        </div>

        <!-- نافذة تاريخ المشتريات -->
        <div id="purchasesHistoryModal" class="modal hidden">
            <div class="modal-content large-modal">
                <div class="modal-header">
                    <h3>تاريخ المشتريات</h3>
                    <button class="btn btn-icon" onclick="hidePurchasesHistoryModal()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="table-container">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>رقم الفاتورة</th>
                                    <th>المورد</th>
                                    <th>المجموع</th>
                                    <th>طريقة الدفع</th>
                                    <th>التاريخ</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="purchasesHistoryTableBody">
                                <!-- سيتم ملء البيانات هنا -->
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" onclick="hidePurchasesHistoryModal()">إغلاق</button>
                </div>
            </div>
        </div>

        <!-- نافذة تفاصيل فاتورة الشراء -->
        <div id="purchaseDetailsModal" class="modal hidden">
            <div class="modal-content large-modal">
                <div class="modal-header">
                    <h3>تفاصيل فاتورة الشراء</h3>
                    <button class="btn btn-icon" onclick="hidePurchaseDetailsModal()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div id="purchaseDetailsContent">
                        <!-- سيتم ملء التفاصيل هنا -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-info" onclick="printPurchaseInvoice()">
                        <i class="fas fa-print"></i>
                        طباعة
                    </button>
                    <button class="btn btn-secondary" onclick="hidePurchaseDetailsModal()">إغلاق</button>
                </div>
            </div>
        </div>
    `;
}

// متغيرات المشتريات
let purchaseItems = [];
let currentPurchase = null;
let purchasesData = [];

// تهيئة صفحة المشتريات
function initializePurchases() {
    loadPurchases();
    loadSuppliersForPurchase();
    updatePurchasesStats();
    updatePurchaseTaxRate();
}

// تحميل المشتريات
function loadPurchases() {
    purchasesData = db.getData('purchases');
    displayPurchases(purchasesData);
}

// عرض المشتريات في الجدول
function displayPurchases(purchases) {
    const tbody = document.getElementById('purchasesTableBody');
    
    if (purchases.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="6" class="text-center">لا توجد مشتريات</td>
            </tr>
        `;
        return;
    }
    
    // ترتيب المشتريات حسب التاريخ (الأحدث أولاً)
    const sortedPurchases = purchases.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
    
    tbody.innerHTML = sortedPurchases.map(purchase => `
        <tr>
            <td>${purchase.invoiceNumber || 'غير محدد'}</td>
            <td>${purchase.supplierName}</td>
            <td>${formatCurrency(purchase.total)}</td>
            <td>${purchase.paymentMethod === 'cash' ? 'نقداً' : 'على الحساب'}</td>
            <td>${formatDateTime(purchase.createdAt)}</td>
            <td>
                <div class="action-buttons">
                    <button class="btn btn-sm btn-info" onclick="viewPurchaseDetails('${purchase.id}')" title="عرض التفاصيل">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-sm btn-success" onclick="printPurchase('${purchase.id}')" title="طباعة">
                        <i class="fas fa-print"></i>
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="deletePurchase('${purchase.id}')" title="حذف">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
}

// تحميل الموردين لفاتورة الشراء
function loadSuppliersForPurchase() {
    const suppliers = db.getData('suppliers');
    const supplierSelect = document.getElementById('purchaseSupplier');
    const supplierFilter = document.getElementById('supplierFilter');
    
    if (supplierSelect) {
        supplierSelect.innerHTML = '<option value="">اختر المورد</option>' +
            suppliers.map(supplier => `<option value="${supplier.id}">${supplier.name}</option>`).join('');
    }
    
    if (supplierFilter) {
        supplierFilter.innerHTML = '<option value="">جميع الموردين</option>' +
            suppliers.map(supplier => `<option value="${supplier.id}">${supplier.name}</option>`).join('');
    }
}

// تحديث معدل الضريبة للمشتريات
function updatePurchaseTaxRate() {
    const settings = db.getSettings();
    const taxRateElement = document.getElementById('purchaseTaxRate');
    if (taxRateElement) {
        taxRateElement.textContent = formatArabicNumber(settings.taxRate);
    }
}

// تحديث إحصائيات المشتريات
function updatePurchasesStats() {
    const purchases = db.getData('purchases');
    const totalAmount = purchases.reduce((sum, purchase) => sum + purchase.total, 0);

    // مشتريات اليوم
    const today = new Date().toDateString();
    const todayAmount = purchases
        .filter(purchase => new Date(purchase.createdAt).toDateString() === today)
        .reduce((sum, purchase) => sum + purchase.total, 0);

    document.getElementById('totalPurchasesAmount').textContent = formatCurrency(totalAmount);
    document.getElementById('todayPurchasesAmount').textContent = formatCurrency(todayAmount);
    document.getElementById('totalPurchasesCount').textContent = formatArabicNumber(purchases.length);
}

// عرض نافذة إضافة فاتورة شراء
function showAddPurchaseModal() {
    purchaseItems = [];
    document.getElementById('purchaseForm').reset();
    updatePurchaseItemsList();
    updatePurchaseSummary();
    document.getElementById('purchaseModal').classList.remove('hidden');
}

// إخفاء نافذة فاتورة الشراء
function hidePurchaseModal() {
    document.getElementById('purchaseModal').classList.add('hidden');
    purchaseItems = [];
}

// إضافة عنصر للفاتورة
function addPurchaseItem() {
    const item = {
        id: Date.now(),
        name: '',
        quantity: 1,
        price: 0,
        total: 0
    };

    purchaseItems.push(item);
    updatePurchaseItemsList();
}

// تحديث قائمة عناصر الفاتورة
function updatePurchaseItemsList() {
    const itemsList = document.getElementById('purchaseItemsList');

    if (purchaseItems.length === 0) {
        itemsList.innerHTML = '<div class="empty-items">لا توجد عناصر في الفاتورة</div>';
        return;
    }

    itemsList.innerHTML = purchaseItems.map((item, index) => `
        <div class="purchase-item">
            <div class="item-fields">
                <div class="field-group">
                    <label>اسم المنتج</label>
                    <input type="text" value="${item.name}" onchange="updatePurchaseItem(${index}, 'name', this.value)" placeholder="اسم المنتج" required>
                </div>
                <div class="field-group">
                    <label>الكمية</label>
                    <input type="number" value="${item.quantity}" onchange="updatePurchaseItem(${index}, 'quantity', this.value)" min="1" required>
                </div>
                <div class="field-group">
                    <label>السعر</label>
                    <input type="number" value="${item.price}" onchange="updatePurchaseItem(${index}, 'price', this.value)" step="0.01" min="0" required>
                </div>
                <div class="field-group">
                    <label>المجموع</label>
                    <span class="item-total">${formatCurrency(item.total)}</span>
                </div>
                <div class="field-group">
                    <button type="button" class="btn btn-sm btn-danger" onclick="removePurchaseItem(${index})">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        </div>
    `).join('');
}

// تحديث عنصر في الفاتورة
function updatePurchaseItem(index, field, value) {
    if (index >= 0 && index < purchaseItems.length) {
        purchaseItems[index][field] = field === 'name' ? value : parseFloat(value) || 0;

        // حساب المجموع للعنصر
        if (field === 'quantity' || field === 'price') {
            purchaseItems[index].total = purchaseItems[index].quantity * purchaseItems[index].price;
        }

        updatePurchaseItemsList();
        updatePurchaseSummary();
    }
}

// إزالة عنصر من الفاتورة
function removePurchaseItem(index) {
    purchaseItems.splice(index, 1);
    updatePurchaseItemsList();
    updatePurchaseSummary();
}

// تحديث ملخص فاتورة الشراء
function updatePurchaseSummary() {
    const subtotal = purchaseItems.reduce((sum, item) => sum + item.total, 0);
    const settings = db.getSettings();
    const taxAmount = subtotal * (settings.taxRate / 100);
    const total = subtotal + taxAmount;

    document.getElementById('purchaseSubtotal').textContent = formatCurrency(subtotal);
    document.getElementById('purchaseTaxAmount').textContent = formatCurrency(taxAmount);
    document.getElementById('purchaseTotal').textContent = formatCurrency(total);
}

// حفظ فاتورة الشراء
function savePurchase() {
    const form = document.getElementById('purchaseForm');
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    const supplierId = document.getElementById('purchaseSupplier').value;
    if (!supplierId) {
        showAlert('يجب اختيار المورد', 'warning');
        return;
    }

    if (purchaseItems.length === 0) {
        showAlert('يجب إضافة عناصر للفاتورة', 'warning');
        return;
    }

    // التحقق من أن جميع العناصر مكتملة
    const incompleteItems = purchaseItems.filter(item => !item.name || item.quantity <= 0 || item.price <= 0);
    if (incompleteItems.length > 0) {
        showAlert('يجب إكمال جميع بيانات العناصر', 'warning');
        return;
    }

    const supplier = db.getData('suppliers').find(s => s.id === supplierId);
    const subtotal = purchaseItems.reduce((sum, item) => sum + item.total, 0);
    const settings = db.getSettings();
    const taxAmount = subtotal * (settings.taxRate / 100);
    const total = subtotal + taxAmount;
    const paymentMethod = document.querySelector('input[name="purchasePaymentMethod"]:checked').value;

    const purchaseData = {
        invoiceNumber: sanitizeInput(document.getElementById('purchaseInvoiceNumber').value) || generatePurchaseInvoiceNumber(),
        supplierId: supplierId,
        supplierName: supplier.name,
        items: [...purchaseItems],
        subtotal: subtotal,
        taxAmount: taxAmount,
        total: total,
        paymentMethod: paymentMethod,
        notes: sanitizeInput(document.getElementById('purchaseNotes').value)
    };

    try {
        // حفظ فاتورة الشراء
        db.addItem('purchases', purchaseData);

        // تحديث المخزون
        updateInventoryAfterPurchase(purchaseItems);

        // تحديث رصيد المورد للمشتريات الآجلة
        if (paymentMethod === 'credit') {
            updateSupplierBalance(supplierId, total);
        }

        showAlert('تم حفظ فاتورة الشراء بنجاح', 'success');
        hidePurchaseModal();
        loadPurchases();
        updatePurchasesStats();
        updateStats();

    } catch (error) {
        showAlert('حدث خطأ أثناء حفظ فاتورة الشراء', 'error');
    }
}

// توليد رقم فاتورة شراء
function generatePurchaseInvoiceNumber() {
    const purchases = db.getData('purchases');
    const today = new Date();
    const dateStr = today.toISOString().split('T')[0].replace(/-/g, '');
    const dailyPurchases = purchases.filter(purchase =>
        purchase.createdAt.split('T')[0] === today.toISOString().split('T')[0]
    );
    const sequenceNumber = (dailyPurchases.length + 1).toString().padStart(3, '0');
    return `PUR-${dateStr}-${sequenceNumber}`;
}

// تحديث المخزون بعد الشراء
function updateInventoryAfterPurchase(items) {
    items.forEach(item => {
        // البحث عن المنتج الموجود
        const products = db.getData('products');
        let existingProduct = products.find(p => p.name.toLowerCase() === item.name.toLowerCase());

        if (existingProduct) {
            // تحديث الكمية والتكلفة
            const newQuantity = existingProduct.quantity + item.quantity;
            const newCost = item.price; // استخدام سعر الشراء كتكلفة
            db.updateItem('products', existingProduct.id, {
                quantity: newQuantity,
                cost: newCost
            });
        } else {
            // إضافة منتج جديد
            const newProduct = {
                name: item.name,
                description: '',
                price: item.price * 1.3, // هامش ربح افتراضي 30%
                cost: item.price,
                quantity: item.quantity,
                category: 'عام',
                minStock: 10,
                barcode: ''
            };
            db.addItem('products', newProduct);
        }
    });
}

// تحديث رصيد المورد
function updateSupplierBalance(supplierId, amount) {
    const supplier = db.getData('suppliers').find(s => s.id === supplierId);
    if (supplier) {
        const newBalance = supplier.balance + amount;
        db.updateItem('suppliers', supplierId, { balance: newBalance });
    }
}

// البحث في المشتريات
function searchPurchases() {
    const searchTerm = document.getElementById('purchaseSearch').value.toLowerCase();
    const filteredPurchases = purchasesData.filter(purchase =>
        (purchase.invoiceNumber && purchase.invoiceNumber.toLowerCase().includes(searchTerm)) ||
        purchase.supplierName.toLowerCase().includes(searchTerm) ||
        (purchase.notes && purchase.notes.toLowerCase().includes(searchTerm))
    );
    displayPurchases(filteredPurchases);
}

// فلترة المشتريات
function filterPurchases() {
    const supplierFilter = document.getElementById('supplierFilter').value;
    const dateFrom = document.getElementById('dateFromFilter').value;
    const dateTo = document.getElementById('dateToFilter').value;

    let filteredPurchases = purchasesData;

    // فلترة حسب المورد
    if (supplierFilter) {
        filteredPurchases = filteredPurchases.filter(purchase => purchase.supplierId === supplierFilter);
    }

    // فلترة حسب التاريخ
    if (dateFrom) {
        filteredPurchases = filteredPurchases.filter(purchase => purchase.createdAt >= dateFrom + 'T00:00:00');
    }

    if (dateTo) {
        filteredPurchases = filteredPurchases.filter(purchase => purchase.createdAt <= dateTo + 'T23:59:59');
    }

    displayPurchases(filteredPurchases);
}

// عرض تاريخ المشتريات
function showPurchasesHistory() {
    loadPurchasesHistory();
    document.getElementById('purchasesHistoryModal').classList.remove('hidden');
}

// إخفاء تاريخ المشتريات
function hidePurchasesHistoryModal() {
    document.getElementById('purchasesHistoryModal').classList.add('hidden');
}

// تحميل تاريخ المشتريات
function loadPurchasesHistory() {
    const purchases = db.getData('purchases');
    displayPurchasesHistory(purchases);
}

// عرض تاريخ المشتريات
function displayPurchasesHistory(purchases) {
    const tbody = document.getElementById('purchasesHistoryTableBody');

    if (purchases.length === 0) {
        tbody.innerHTML = '<tr><td colspan="6" class="text-center">لا توجد مشتريات</td></tr>';
        return;
    }

    // ترتيب المشتريات حسب التاريخ (الأحدث أولاً)
    const sortedPurchases = purchases.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

    tbody.innerHTML = sortedPurchases.map(purchase => `
        <tr>
            <td>${purchase.invoiceNumber || 'غير محدد'}</td>
            <td>${purchase.supplierName}</td>
            <td>${formatCurrency(purchase.total)}</td>
            <td>${purchase.paymentMethod === 'cash' ? 'نقداً' : 'على الحساب'}</td>
            <td>${formatDateTime(purchase.createdAt)}</td>
            <td>
                <div class="action-buttons">
                    <button class="btn btn-sm btn-info" onclick="viewPurchaseDetailsFromHistory('${purchase.id}')" title="عرض التفاصيل">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-sm btn-success" onclick="printPurchaseFromHistory('${purchase.id}')" title="طباعة">
                        <i class="fas fa-print"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
}

// عرض تفاصيل فاتورة الشراء
function viewPurchaseDetails(purchaseId) {
    const purchase = purchasesData.find(p => p.id === purchaseId);
    if (purchase) {
        showPurchaseDetails(purchase);
    }
}

// عرض تفاصيل فاتورة الشراء من التاريخ
function viewPurchaseDetailsFromHistory(purchaseId) {
    const purchase = db.getData('purchases').find(p => p.id === purchaseId);
    if (purchase) {
        hidePurchasesHistoryModal();
        showPurchaseDetails(purchase);
    }
}

// عرض تفاصيل فاتورة الشراء
function showPurchaseDetails(purchase) {
    currentPurchase = purchase;
    const settings = db.getSettings();

    document.getElementById('purchaseDetailsContent').innerHTML = `
        <div class="purchase-details">
            <div class="purchase-header">
                <div class="company-info">
                    <h2>${settings.companyName}</h2>
                    <p>${settings.companyAddress}</p>
                    <p>هاتف: ${settings.companyPhone}</p>
                    <p>بريد إلكتروني: ${settings.companyEmail}</p>
                </div>
                <div class="purchase-info">
                    <h3>فاتورة مشتريات</h3>
                    <p><strong>رقم الفاتورة:</strong> ${purchase.invoiceNumber}</p>
                    <p><strong>التاريخ:</strong> ${formatDateTime(purchase.createdAt)}</p>
                    <p><strong>المورد:</strong> ${purchase.supplierName}</p>
                    <p><strong>طريقة الدفع:</strong> ${purchase.paymentMethod === 'cash' ? 'نقداً' : 'على الحساب'}</p>
                    ${purchase.notes ? `<p><strong>ملاحظات:</strong> ${purchase.notes}</p>` : ''}
                </div>
            </div>

            <div class="purchase-items">
                <table class="invoice-table">
                    <thead>
                        <tr>
                            <th>المنتج</th>
                            <th>الكمية</th>
                            <th>السعر</th>
                            <th>المجموع</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${purchase.items.map(item => `
                            <tr>
                                <td>${item.name}</td>
                                <td>${formatArabicNumber(item.quantity)}</td>
                                <td>${formatCurrency(item.price)}</td>
                                <td>${formatCurrency(item.total)}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>

            <div class="purchase-summary">
                <div class="summary-row">
                    <span>المجموع الفرعي:</span>
                    <span>${formatCurrency(purchase.subtotal)}</span>
                </div>
                <div class="summary-row">
                    <span>الضريبة (${formatArabicNumber(settings.taxRate)}%):</span>
                    <span>${formatCurrency(purchase.taxAmount)}</span>
                </div>
                <div class="summary-row total">
                    <span>المجموع الكلي:</span>
                    <span>${formatCurrency(purchase.total)}</span>
                </div>
            </div>
        </div>
    `;

    document.getElementById('purchaseDetailsModal').classList.remove('hidden');
}

// إخفاء تفاصيل فاتورة الشراء
function hidePurchaseDetailsModal() {
    document.getElementById('purchaseDetailsModal').classList.add('hidden');
    currentPurchase = null;
}

// طباعة فاتورة الشراء
function printPurchaseInvoice() {
    if (!currentPurchase) return;

    const content = document.getElementById('purchaseDetailsContent').innerHTML;
    printContent(content, `فاتورة شراء رقم ${currentPurchase.invoiceNumber}`);
}

// طباعة فاتورة شراء
function printPurchase(purchaseId) {
    const purchase = purchasesData.find(p => p.id === purchaseId);
    if (purchase) {
        const originalPurchase = currentPurchase;
        showPurchaseDetails(purchase);
        setTimeout(() => {
            printPurchaseInvoice();
            hidePurchaseDetailsModal();
            currentPurchase = originalPurchase;
        }, 100);
    }
}

// طباعة فاتورة شراء من التاريخ
function printPurchaseFromHistory(purchaseId) {
    const purchase = db.getData('purchases').find(p => p.id === purchaseId);
    if (purchase) {
        const originalPurchase = currentPurchase;
        showPurchaseDetails(purchase);
        setTimeout(() => {
            printPurchaseInvoice();
            hidePurchaseDetailsModal();
            currentPurchase = originalPurchase;
        }, 100);
    }
}

// حذف فاتورة شراء
function deletePurchase(purchaseId) {
    const purchase = purchasesData.find(p => p.id === purchaseId);
    if (!purchase) return;

    showConfirm(`هل أنت متأكد من حذف فاتورة الشراء "${purchase.invoiceNumber}"؟`, function() {
        try {
            // حذف الفاتورة
            db.deleteItem('purchases', purchaseId);

            // يمكن إضافة منطق لتعديل المخزون إذا لزم الأمر
            // لكن هذا قد يكون معقداً إذا تم بيع المنتجات بالفعل

            showAlert('تم حذف فاتورة الشراء بنجاح', 'success');
            loadPurchases();
            updatePurchasesStats();
            updateStats();
        } catch (error) {
            showAlert('حدث خطأ أثناء حذف فاتورة الشراء', 'error');
        }
    });
}
