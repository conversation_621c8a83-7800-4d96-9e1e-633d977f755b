// إدارة المنتجات
function getProductsHTML() {
    return `
        <div class="products-container">
            <div class="page-header">
                <h2 class="page-title">إدارة المنتجات</h2>
                <div class="page-actions">
                    <button class="btn btn-primary" onclick="showAddProductModal()">
                        <i class="fas fa-plus"></i>
                        إضافة منتج جديد
                    </button>
                    <button class="btn btn-success" onclick="exportProducts()">
                        <i class="fas fa-download"></i>
                        تصدير
                    </button>
                </div>
            </div>

            <!-- شريط البحث والفلترة -->
            <div class="search-filter-bar">
                <div class="search-group">
                    <div class="input-group">
                        <input type="text" id="productSearch" placeholder="البحث في المنتجات..." onkeyup="searchProducts()">
                        <i class="fas fa-search"></i>
                    </div>
                </div>
                <div class="filter-group">
                    <select id="categoryFilter" onchange="filterProducts()">
                        <option value="">جميع الفئات</option>
                    </select>
                    <select id="stockFilter" onchange="filterProducts()">
                        <option value="">جميع المنتجات</option>
                        <option value="low">مخزون منخفض</option>
                        <option value="out">نفد المخزون</option>
                    </select>
                </div>
            </div>

            <!-- جدول المنتجات -->
            <div class="table-container">
                <table class="data-table" id="productsTable">
                    <thead>
                        <tr>
                            <th>الاسم</th>
                            <th>الفئة</th>
                            <th>السعر</th>
                            <th>التكلفة</th>
                            <th>الكمية</th>
                            <th>الحد الأدنى</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="productsTableBody">
                        <!-- سيتم ملء البيانات هنا -->
                    </tbody>
                </table>
            </div>

            <!-- إحصائيات المنتجات -->
            <div class="products-stats">
                <div class="stat-item">
                    <span class="label">إجمالي المنتجات:</span>
                    <span class="value" id="totalProductsCount">٠</span>
                </div>
                <div class="stat-item">
                    <span class="label">قيمة المخزون:</span>
                    <span class="value" id="totalInventoryValue">٠.٠٠ ريال</span>
                </div>
                <div class="stat-item">
                    <span class="label">منتجات منخفضة المخزون:</span>
                    <span class="value" id="lowStockCount">٠</span>
                </div>
            </div>
        </div>

        <!-- نافذة إضافة/تعديل منتج -->
        <div id="productModal" class="modal hidden">
            <div class="modal-content large-modal">
                <div class="modal-header">
                    <h3 id="productModalTitle">إضافة منتج جديد</h3>
                    <button class="btn btn-icon" onclick="hideProductModal()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="productForm">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="productName">اسم المنتج *</label>
                                <input type="text" id="productName" required>
                            </div>
                            <div class="form-group">
                                <label for="productCategory">الفئة</label>
                                <input type="text" id="productCategory" list="categoriesList">
                                <datalist id="categoriesList"></datalist>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="productDescription">الوصف</label>
                            <textarea id="productDescription" rows="3"></textarea>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="productPrice">سعر البيع *</label>
                                <input type="number" id="productPrice" step="0.01" min="0" required>
                            </div>
                            <div class="form-group">
                                <label for="productCost">سعر التكلفة</label>
                                <input type="number" id="productCost" step="0.01" min="0">
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="productQuantity">الكمية الحالية *</label>
                                <input type="number" id="productQuantity" min="0" required>
                            </div>
                            <div class="form-group">
                                <label for="productMinStock">الحد الأدنى للمخزون</label>
                                <input type="number" id="productMinStock" min="0" value="10">
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="productBarcode">الباركود</label>
                            <input type="text" id="productBarcode">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" onclick="hideProductModal()">إلغاء</button>
                    <button class="btn btn-primary" onclick="saveProduct()">حفظ</button>
                </div>
            </div>
        </div>

        <!-- نافذة تفاصيل المنتج -->
        <div id="productDetailsModal" class="modal hidden">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>تفاصيل المنتج</h3>
                    <button class="btn btn-icon" onclick="hideProductDetailsModal()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div id="productDetailsContent">
                        <!-- سيتم ملء التفاصيل هنا -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" onclick="hideProductDetailsModal()">إغلاق</button>
                </div>
            </div>
        </div>
    `;
}

// متغيرات المنتجات
let currentProductId = null;
let productsData = [];

// تهيئة صفحة المنتجات
function initializeProducts() {
    loadProducts();
    loadCategories();
    updateProductsStats();
}

// تحميل المنتجات
function loadProducts() {
    productsData = db.getData('products');
    displayProducts(productsData);
}

// عرض المنتجات في الجدول
function displayProducts(products) {
    const tbody = document.getElementById('productsTableBody');
    
    if (products.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="8" class="text-center">لا توجد منتجات</td>
            </tr>
        `;
        return;
    }
    
    tbody.innerHTML = products.map(product => {
        const stockStatus = getStockStatus(product);
        return `
            <tr>
                <td>
                    <div class="product-info">
                        <strong>${product.name}</strong>
                        ${product.description ? `<br><small class="text-muted">${product.description}</small>` : ''}
                    </div>
                </td>
                <td>${product.category || 'غير محدد'}</td>
                <td>${formatCurrency(product.price)}</td>
                <td>${formatCurrency(product.cost || 0)}</td>
                <td>${formatArabicNumber(product.quantity)}</td>
                <td>${formatArabicNumber(product.minStock)}</td>
                <td>
                    <span class="status-badge ${stockStatus.class}">${stockStatus.text}</span>
                </td>
                <td>
                    <div class="action-buttons">
                        <button class="btn btn-sm btn-info" onclick="showProductDetails('${product.id}')" title="عرض التفاصيل">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-warning" onclick="editProduct('${product.id}')" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="deleteProduct('${product.id}')" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }).join('');
}

// تحديد حالة المخزون
function getStockStatus(product) {
    if (product.quantity === 0) {
        return { class: 'status-danger', text: 'نفد المخزون' };
    } else if (product.quantity <= product.minStock) {
        return { class: 'status-warning', text: 'مخزون منخفض' };
    } else {
        return { class: 'status-success', text: 'متوفر' };
    }
}

// تحميل الفئات
function loadCategories() {
    const products = db.getData('products');
    const categories = [...new Set(products.map(p => p.category).filter(c => c))];
    
    const categoryFilter = document.getElementById('categoryFilter');
    const categoriesList = document.getElementById('categoriesList');
    
    // تحديث فلتر الفئات
    categoryFilter.innerHTML = '<option value="">جميع الفئات</option>' +
        categories.map(cat => `<option value="${cat}">${cat}</option>`).join('');
    
    // تحديث قائمة الفئات للإدخال التلقائي
    categoriesList.innerHTML = categories.map(cat => `<option value="${cat}">`).join('');
}

// البحث في المنتجات
function searchProducts() {
    const searchTerm = document.getElementById('productSearch').value.toLowerCase();
    const filteredProducts = productsData.filter(product =>
        product.name.toLowerCase().includes(searchTerm) ||
        (product.description && product.description.toLowerCase().includes(searchTerm)) ||
        (product.category && product.category.toLowerCase().includes(searchTerm)) ||
        (product.barcode && product.barcode.includes(searchTerm))
    );
    displayProducts(filteredProducts);
}

// فلترة المنتجات
function filterProducts() {
    const categoryFilter = document.getElementById('categoryFilter').value;
    const stockFilter = document.getElementById('stockFilter').value;
    
    let filteredProducts = productsData;
    
    // فلترة حسب الفئة
    if (categoryFilter) {
        filteredProducts = filteredProducts.filter(product => product.category === categoryFilter);
    }
    
    // فلترة حسب حالة المخزون
    if (stockFilter === 'low') {
        filteredProducts = filteredProducts.filter(product => product.quantity <= product.minStock && product.quantity > 0);
    } else if (stockFilter === 'out') {
        filteredProducts = filteredProducts.filter(product => product.quantity === 0);
    }
    
    displayProducts(filteredProducts);
}

// تحديث إحصائيات المنتجات
function updateProductsStats() {
    const products = db.getData('products');
    const totalProducts = products.length;
    const totalValue = products.reduce((sum, product) => sum + (product.quantity * (product.cost || product.price)), 0);
    const lowStockCount = products.filter(product => product.quantity <= product.minStock).length;

    document.getElementById('totalProductsCount').textContent = formatArabicNumber(totalProducts);
    document.getElementById('totalInventoryValue').textContent = formatCurrency(totalValue);
    document.getElementById('lowStockCount').textContent = formatArabicNumber(lowStockCount);
}

// عرض نافذة إضافة منتج
function showAddProductModal() {
    currentProductId = null;
    document.getElementById('productModalTitle').textContent = 'إضافة منتج جديد';
    document.getElementById('productForm').reset();
    document.getElementById('productModal').classList.remove('hidden');
}

// إخفاء نافذة المنتج
function hideProductModal() {
    document.getElementById('productModal').classList.add('hidden');
    currentProductId = null;
}

// تعديل منتج
function editProduct(productId) {
    const product = productsData.find(p => p.id === productId);
    if (!product) return;

    currentProductId = productId;
    document.getElementById('productModalTitle').textContent = 'تعديل المنتج';

    // ملء النموذج بالبيانات
    document.getElementById('productName').value = product.name;
    document.getElementById('productCategory').value = product.category || '';
    document.getElementById('productDescription').value = product.description || '';
    document.getElementById('productPrice').value = product.price;
    document.getElementById('productCost').value = product.cost || '';
    document.getElementById('productQuantity').value = product.quantity;
    document.getElementById('productMinStock').value = product.minStock;
    document.getElementById('productBarcode').value = product.barcode || '';

    document.getElementById('productModal').classList.remove('hidden');
}

// حفظ المنتج
function saveProduct() {
    const form = document.getElementById('productForm');
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    const productData = {
        name: sanitizeInput(document.getElementById('productName').value),
        category: sanitizeInput(document.getElementById('productCategory').value),
        description: sanitizeInput(document.getElementById('productDescription').value),
        price: parseFloat(document.getElementById('productPrice').value),
        cost: parseFloat(document.getElementById('productCost').value) || 0,
        quantity: parseInt(document.getElementById('productQuantity').value),
        minStock: parseInt(document.getElementById('productMinStock').value) || 10,
        barcode: sanitizeInput(document.getElementById('productBarcode').value)
    };

    try {
        if (currentProductId) {
            // تحديث منتج موجود
            db.updateItem('products', currentProductId, productData);
            showAlert('تم تحديث المنتج بنجاح', 'success');
        } else {
            // إضافة منتج جديد
            db.addItem('products', productData);
            showAlert('تم إضافة المنتج بنجاح', 'success');
        }

        hideProductModal();
        loadProducts();
        loadCategories();
        updateProductsStats();
        updateStats(); // تحديث إحصائيات لوحة المعلومات

    } catch (error) {
        showAlert('حدث خطأ أثناء حفظ المنتج', 'error');
    }
}

// حذف منتج
function deleteProduct(productId) {
    const product = productsData.find(p => p.id === productId);
    if (!product) return;

    showConfirm(`هل أنت متأكد من حذف المنتج "${product.name}"؟`, function() {
        try {
            db.deleteItem('products', productId);
            showAlert('تم حذف المنتج بنجاح', 'success');
            loadProducts();
            loadCategories();
            updateProductsStats();
            updateStats();
        } catch (error) {
            showAlert('حدث خطأ أثناء حذف المنتج', 'error');
        }
    });
}

// عرض تفاصيل المنتج
function showProductDetails(productId) {
    const product = productsData.find(p => p.id === productId);
    if (!product) return;

    const stockStatus = getStockStatus(product);
    const profit = product.price - (product.cost || 0);
    const profitMargin = product.cost ? ((profit / product.price) * 100).toFixed(2) : 0;

    document.getElementById('productDetailsContent').innerHTML = `
        <div class="product-details">
            <div class="detail-row">
                <span class="detail-label">اسم المنتج:</span>
                <span class="detail-value">${product.name}</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">الفئة:</span>
                <span class="detail-value">${product.category || 'غير محدد'}</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">الوصف:</span>
                <span class="detail-value">${product.description || 'لا يوجد وصف'}</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">سعر البيع:</span>
                <span class="detail-value">${formatCurrency(product.price)}</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">سعر التكلفة:</span>
                <span class="detail-value">${formatCurrency(product.cost || 0)}</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">الربح المتوقع:</span>
                <span class="detail-value">${formatCurrency(profit)} (${formatArabicNumber(profitMargin)}%)</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">الكمية الحالية:</span>
                <span class="detail-value">${formatArabicNumber(product.quantity)}</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">الحد الأدنى:</span>
                <span class="detail-value">${formatArabicNumber(product.minStock)}</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">حالة المخزون:</span>
                <span class="detail-value">
                    <span class="status-badge ${stockStatus.class}">${stockStatus.text}</span>
                </span>
            </div>
            <div class="detail-row">
                <span class="detail-label">الباركود:</span>
                <span class="detail-value">${product.barcode || 'غير محدد'}</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">قيمة المخزون:</span>
                <span class="detail-value">${formatCurrency(product.quantity * (product.cost || product.price))}</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">تاريخ الإضافة:</span>
                <span class="detail-value">${formatDateTime(product.createdAt)}</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">آخر تحديث:</span>
                <span class="detail-value">${formatDateTime(product.updatedAt)}</span>
            </div>
        </div>
    `;

    document.getElementById('productDetailsModal').classList.remove('hidden');
}

// إخفاء نافذة تفاصيل المنتج
function hideProductDetailsModal() {
    document.getElementById('productDetailsModal').classList.add('hidden');
}

// تصدير المنتجات
function exportProducts() {
    try {
        const products = db.getData('products');
        const csvContent = generateProductsCSV(products);
        downloadCSV(csvContent, 'products.csv');
        showAlert('تم تصدير المنتجات بنجاح', 'success');
    } catch (error) {
        showAlert('حدث خطأ أثناء تصدير المنتجات', 'error');
    }
}

// توليد ملف CSV للمنتجات
function generateProductsCSV(products) {
    const headers = ['الاسم', 'الفئة', 'الوصف', 'سعر البيع', 'سعر التكلفة', 'الكمية', 'الحد الأدنى', 'الباركود'];
    const rows = products.map(product => [
        product.name,
        product.category || '',
        product.description || '',
        product.price,
        product.cost || 0,
        product.quantity,
        product.minStock,
        product.barcode || ''
    ]);

    return [headers, ...rows].map(row => row.join(',')).join('\n');
}

// تحميل ملف CSV
function downloadCSV(content, filename) {
    const blob = new Blob([content], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}
