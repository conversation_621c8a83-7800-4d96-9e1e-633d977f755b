# نظام إدارة نقاط البيع (ERP System)

نظام شامل لإدارة نقاط البيع والمخزون مصمم خصيصاً للشركات الصغيرة والمتوسطة. يوفر النظام واجهة سهلة الاستخدام باللغة العربية مع جميع الميزات الأساسية لإدارة الأعمال التجارية.

## الميزات الرئيسية

### 📊 لوحة التحكم
- عرض الإحصائيات الرئيسية للمبيعات والأرباح
- مخططات بيانية تفاعلية
- تنبيهات المخزون المنخفض
- ملخص الأداء اليومي والشهري

### 🛒 نظام المبيعات
- إنشاء فواتير مبيعات سريعة وسهلة
- دعم الباركود للمنتجات
- حساب الضرائب تلقائياً
- طرق دفع متعددة (نقدي/آجل)
- طباعة الفواتير

### 📦 إدارة المنتجات
- إضافة وتعديل المنتجات
- تصنيف المنتجات حسب الفئات
- تتبع المخزون والكميات
- تنبيهات المخزون المنخفض
- إدارة الأسعار والتكاليف

### 👥 إدارة العملاء
- قاعدة بيانات شاملة للعملاء
- تتبع أرصدة العملاء
- تاريخ المعاملات لكل عميل
- إدارة الديون والمدفوعات

### 🚚 إدارة الموردين
- قاعدة بيانات الموردين
- تتبع المستحقات للموردين
- تاريخ المشتريات والدفعات

### 🛍️ نظام المشتريات
- إنشاء فواتير شراء
- تحديث المخزون تلقائياً
- ربط المشتريات بالموردين
- تتبع التكاليف

### 💳 إدارة الديون
- تتبع ديون العملاء
- إرسال تذكيرات الدفع
- تحليل الديون المتأخرة
- إدارة المدفوعات

### 📈 التقارير والإحصائيات
- تقارير المبيعات المفصلة
- تقارير المخزون
- تقارير العملاء والموردين
- التقارير المالية
- تصدير التقارير بصيغة CSV

### ⚙️ الإعدادات
- إعدادات الشركة
- إعدادات النظام والضرائب
- تخصيص المظهر والألوان
- النسخ الاحتياطي واستعادة البيانات

## التقنيات المستخدمة

- **HTML5** - هيكل الصفحات
- **CSS3** - التصميم والتنسيق
- **JavaScript** - المنطق والتفاعل
- **LocalStorage** - تخزين البيانات محلياً
- **Font Awesome** - الأيقونات
- **Google Fonts** - خط Cairo العربي

## متطلبات التشغيل

- متصفح ويب حديث (Chrome, Firefox, Safari, Edge)
- لا يتطلب خادم ويب أو قاعدة بيانات خارجية
- يعمل محلياً على الجهاز

## طريقة التشغيل

1. قم بتحميل جميع الملفات في مجلد واحد
2. افتح ملف `index.html` في المتصفح
3. استخدم كلمة المرور الافتراضية: `admin`
4. ابدأ في استخدام النظام

## هيكل الملفات

```
ERP-System/
├── index.html          # الصفحة الرئيسية
├── style.css           # ملف التنسيق الرئيسي
├── main.js             # الملف الرئيسي للتطبيق
├── database.js         # إدارة قاعدة البيانات
├── dashboard.js        # لوحة التحكم
├── products.js         # إدارة المنتجات
├── sales.js            # نظام المبيعات
├── customers.js        # إدارة العملاء
├── suppliers.js        # إدارة الموردين
├── purchases.js        # نظام المشتريات
├── debts.js            # إدارة الديون
├── reports.js          # التقارير والإحصائيات
├── settings.js         # الإعدادات
└── README.md           # ملف التوثيق
```

## الاستخدام

### تسجيل الدخول
- كلمة المرور الافتراضية: `admin`
- يمكن تغيير كلمة المرور من الإعدادات

### إضافة منتج جديد
1. اذهب إلى صفحة "المنتجات"
2. انقر على "إضافة منتج جديد"
3. املأ البيانات المطلوبة
4. احفظ المنتج

### إنشاء فاتورة مبيعات
1. اذهب إلى صفحة "المبيعات"
2. انقر على "فاتورة جديدة"
3. اختر العميل وأضف المنتجات
4. احفظ واطبع الفاتورة

### إنشاء نسخة احتياطية
1. اذهب إلى "الإعدادات" > "النسخ الاحتياطي"
2. انقر على "إنشاء نسخة احتياطية"
3. سيتم تحميل ملف JSON يحتوي على جميع البيانات

## الميزات المتقدمة

- **البحث السريع**: بحث فوري في جميع البيانات
- **الفلترة المتقدمة**: فلترة البيانات حسب معايير متعددة
- **التصدير**: تصدير البيانات والتقارير بصيغة CSV
- **الطباعة**: طباعة الفواتير والتقارير
- **التنبيهات**: تنبيهات المخزون المنخفض والديون المتأخرة
- **الثيمات**: دعم الوضع الفاتح والداكن
- **التصميم المتجاوب**: يعمل على جميع الأجهزة

## الدعم والمساعدة

النظام مصمم ليكون سهل الاستخدام وبديهي. في حالة وجود أي استفسارات أو مشاكل:

1. تأكد من استخدام متصفح حديث
2. تأكد من تفعيل JavaScript
3. راجع ملف التوثيق هذا
4. تحقق من وحدة تحكم المطور للأخطاء

## الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام الشخصي والتجاري.

## المساهمة

نرحب بالمساهمات لتحسين النظام. يمكنك:
- الإبلاغ عن الأخطاء
- اقتراح ميزات جديدة
- تحسين الكود الموجود
- ترجمة النظام للغات أخرى

---

**تم تطوير هذا النظام بعناية ليلبي احتياجات الشركات الصغيرة والمتوسطة في إدارة أعمالها بكفاءة وسهولة.**
