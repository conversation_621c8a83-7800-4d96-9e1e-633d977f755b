// إدارة الموردين
function getSuppliersHTML() {
    return `
        <div class="suppliers-container">
            <div class="page-header">
                <h2 class="page-title">إدارة الموردين</h2>
                <div class="page-actions">
                    <button class="btn btn-primary" onclick="showAddSupplierModal()">
                        <i class="fas fa-plus"></i>
                        إضافة مورد جديد
                    </button>
                    <button class="btn btn-success" onclick="exportSuppliers()">
                        <i class="fas fa-download"></i>
                        تصدير
                    </button>
                </div>
            </div>

            <!-- شريط البحث والفلترة -->
            <div class="search-filter-bar">
                <div class="search-group">
                    <div class="input-group">
                        <input type="text" id="supplierSearch" placeholder="البحث في الموردين..." onkeyup="searchSuppliers()">
                        <i class="fas fa-search"></i>
                    </div>
                </div>
                <div class="filter-group">
                    <select id="supplierBalanceFilter" onchange="filterSuppliers()">
                        <option value="">جميع الموردين</option>
                        <option value="creditors">موردين دائنون</option>
                        <option value="debtors">موردين مدينون</option>
                        <option value="zero">رصيد صفر</option>
                    </select>
                </div>
            </div>

            <!-- جدول الموردين -->
            <div class="table-container">
                <table class="data-table" id="suppliersTable">
                    <thead>
                        <tr>
                            <th>الاسم</th>
                            <th>الهاتف</th>
                            <th>البريد الإلكتروني</th>
                            <th>العنوان</th>
                            <th>الرصيد</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="suppliersTableBody">
                        <!-- سيتم ملء البيانات هنا -->
                    </tbody>
                </table>
            </div>

            <!-- إحصائيات الموردين -->
            <div class="suppliers-stats">
                <div class="stat-item">
                    <span class="label">إجمالي الموردين:</span>
                    <span class="value" id="totalSuppliersCount">٠</span>
                </div>
                <div class="stat-item">
                    <span class="label">الموردين الدائنون:</span>
                    <span class="value" id="creditorSuppliersCount">٠</span>
                </div>
                <div class="stat-item">
                    <span class="label">إجمالي المستحقات:</span>
                    <span class="value" id="totalPayablesAmount">٠.٠٠ ريال</span>
                </div>
            </div>
        </div>

        <!-- نافذة إضافة/تعديل مورد -->
        <div id="supplierModal" class="modal hidden">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 id="supplierModalTitle">إضافة مورد جديد</h3>
                    <button class="btn btn-icon" onclick="hideSupplierModal()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="supplierForm">
                        <div class="form-group">
                            <label for="supplierName">اسم المورد *</label>
                            <input type="text" id="supplierName" required>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="supplierPhone">رقم الهاتف</label>
                                <input type="tel" id="supplierPhone">
                            </div>
                            <div class="form-group">
                                <label for="supplierEmail">البريد الإلكتروني</label>
                                <input type="email" id="supplierEmail">
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="supplierAddress">العنوان</label>
                            <textarea id="supplierAddress" rows="3"></textarea>
                        </div>
                        
                        <div class="form-group">
                            <label for="supplierBalance">الرصيد الابتدائي</label>
                            <input type="number" id="supplierBalance" step="0.01" value="0">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" onclick="hideSupplierModal()">إلغاء</button>
                    <button class="btn btn-primary" onclick="saveSupplier()">حفظ</button>
                </div>
            </div>
        </div>

        <!-- نافذة تفاصيل المورد -->
        <div id="supplierDetailsModal" class="modal hidden">
            <div class="modal-content large-modal">
                <div class="modal-header">
                    <h3>تفاصيل المورد</h3>
                    <button class="btn btn-icon" onclick="hideSupplierDetailsModal()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div id="supplierDetailsContent">
                        <!-- سيتم ملء التفاصيل هنا -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-info" onclick="printSupplierStatement()">
                        <i class="fas fa-print"></i>
                        طباعة كشف الحساب
                    </button>
                    <button class="btn btn-secondary" onclick="hideSupplierDetailsModal()">إغلاق</button>
                </div>
            </div>
        </div>

        <!-- نافذة إضافة دفعة للمورد -->
        <div id="supplierPaymentModal" class="modal hidden">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>دفع للمورد</h3>
                    <button class="btn btn-icon" onclick="hideSupplierPaymentModal()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="supplierPaymentForm">
                        <div class="form-group">
                            <label for="supplierPaymentAmount">مبلغ الدفعة *</label>
                            <input type="number" id="supplierPaymentAmount" step="0.01" min="0" required>
                        </div>
                        <div class="form-group">
                            <label for="supplierPaymentNotes">ملاحظات</label>
                            <textarea id="supplierPaymentNotes" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" onclick="hideSupplierPaymentModal()">إلغاء</button>
                    <button class="btn btn-primary" onclick="saveSupplierPayment()">حفظ الدفعة</button>
                </div>
            </div>
        </div>
    `;
}

// متغيرات الموردين
let currentSupplierId = null;
let suppliersData = [];

// تهيئة صفحة الموردين
function initializeSuppliers() {
    loadSuppliers();
    updateSuppliersStats();
}

// تحميل الموردين
function loadSuppliers() {
    suppliersData = db.getData('suppliers');
    displaySuppliers(suppliersData);
}

// عرض الموردين في الجدول
function displaySuppliers(suppliers) {
    const tbody = document.getElementById('suppliersTableBody');
    
    if (suppliers.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="6" class="text-center">لا توجد موردين</td>
            </tr>
        `;
        return;
    }
    
    tbody.innerHTML = suppliers.map(supplier => {
        const balanceStatus = getSupplierBalanceStatus(supplier.balance);
        return `
            <tr>
                <td>
                    <div class="supplier-info">
                        <strong>${supplier.name}</strong>
                    </div>
                </td>
                <td>${supplier.phone || '-'}</td>
                <td>${supplier.email || '-'}</td>
                <td>${supplier.address || '-'}</td>
                <td>
                    <span class="balance-amount ${balanceStatus.class}">
                        ${formatCurrency(supplier.balance)}
                    </span>
                </td>
                <td>
                    <div class="action-buttons">
                        <button class="btn btn-sm btn-info" onclick="showSupplierDetails('${supplier.id}')" title="عرض التفاصيل">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-success" onclick="showSupplierPaymentModal('${supplier.id}')" title="دفع للمورد">
                            <i class="fas fa-money-bill"></i>
                        </button>
                        <button class="btn btn-sm btn-warning" onclick="editSupplier('${supplier.id}')" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="deleteSupplier('${supplier.id}')" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }).join('');
}

// تحديد حالة رصيد المورد
function getSupplierBalanceStatus(balance) {
    if (balance > 0) {
        return { class: 'balance-negative', text: 'مستحق للمورد' }; // المبلغ مستحق للمورد
    } else if (balance < 0) {
        return { class: 'balance-positive', text: 'مستحق من المورد' }; // المبلغ مستحق من المورد
    } else {
        return { class: 'balance-zero', text: 'صفر' };
    }
}

// البحث في الموردين
function searchSuppliers() {
    const searchTerm = document.getElementById('supplierSearch').value.toLowerCase();
    const filteredSuppliers = suppliersData.filter(supplier =>
        supplier.name.toLowerCase().includes(searchTerm) ||
        (supplier.phone && supplier.phone.includes(searchTerm)) ||
        (supplier.email && supplier.email.toLowerCase().includes(searchTerm)) ||
        (supplier.address && supplier.address.toLowerCase().includes(searchTerm))
    );
    displaySuppliers(filteredSuppliers);
}

// فلترة الموردين
function filterSuppliers() {
    const balanceFilter = document.getElementById('supplierBalanceFilter').value;
    
    let filteredSuppliers = suppliersData;
    
    if (balanceFilter === 'creditors') {
        filteredSuppliers = filteredSuppliers.filter(supplier => supplier.balance > 0);
    } else if (balanceFilter === 'debtors') {
        filteredSuppliers = filteredSuppliers.filter(supplier => supplier.balance < 0);
    } else if (balanceFilter === 'zero') {
        filteredSuppliers = filteredSuppliers.filter(supplier => supplier.balance === 0);
    }
    
    displaySuppliers(filteredSuppliers);
}

// تحديث إحصائيات الموردين
function updateSuppliersStats() {
    const suppliers = db.getData('suppliers');
    const totalSuppliers = suppliers.length;
    const creditorSuppliers = suppliers.filter(supplier => supplier.balance > 0).length;
    const totalPayables = suppliers
        .filter(supplier => supplier.balance > 0)
        .reduce((sum, supplier) => sum + supplier.balance, 0);

    document.getElementById('totalSuppliersCount').textContent = formatArabicNumber(totalSuppliers);
    document.getElementById('creditorSuppliersCount').textContent = formatArabicNumber(creditorSuppliers);
    document.getElementById('totalPayablesAmount').textContent = formatCurrency(totalPayables);
}

// عرض نافذة إضافة مورد
function showAddSupplierModal() {
    currentSupplierId = null;
    document.getElementById('supplierModalTitle').textContent = 'إضافة مورد جديد';
    document.getElementById('supplierForm').reset();
    document.getElementById('supplierBalance').value = '0';
    document.getElementById('supplierModal').classList.remove('hidden');
}

// إخفاء نافذة المورد
function hideSupplierModal() {
    document.getElementById('supplierModal').classList.add('hidden');
    currentSupplierId = null;
}

// تعديل مورد
function editSupplier(supplierId) {
    const supplier = suppliersData.find(s => s.id === supplierId);
    if (!supplier) return;

    currentSupplierId = supplierId;
    document.getElementById('supplierModalTitle').textContent = 'تعديل المورد';

    // ملء النموذج بالبيانات
    document.getElementById('supplierName').value = supplier.name;
    document.getElementById('supplierPhone').value = supplier.phone || '';
    document.getElementById('supplierEmail').value = supplier.email || '';
    document.getElementById('supplierAddress').value = supplier.address || '';
    document.getElementById('supplierBalance').value = supplier.balance;

    document.getElementById('supplierModal').classList.remove('hidden');
}

// حفظ المورد
function saveSupplier() {
    const form = document.getElementById('supplierForm');
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    const email = document.getElementById('supplierEmail').value;
    const phone = document.getElementById('supplierPhone').value;

    // التحقق من صحة البريد الإلكتروني
    if (email && !validateEmail(email)) {
        showAlert('البريد الإلكتروني غير صحيح', 'warning');
        return;
    }

    // التحقق من صحة رقم الهاتف
    if (phone && !validatePhone(phone)) {
        showAlert('رقم الهاتف غير صحيح', 'warning');
        return;
    }

    const supplierData = {
        name: sanitizeInput(document.getElementById('supplierName').value),
        phone: sanitizeInput(phone),
        email: sanitizeInput(email),
        address: sanitizeInput(document.getElementById('supplierAddress').value),
        balance: parseFloat(document.getElementById('supplierBalance').value) || 0
    };

    try {
        if (currentSupplierId) {
            // تحديث مورد موجود
            db.updateItem('suppliers', currentSupplierId, supplierData);
            showAlert('تم تحديث المورد بنجاح', 'success');
        } else {
            // إضافة مورد جديد
            db.addItem('suppliers', supplierData);
            showAlert('تم إضافة المورد بنجاح', 'success');
        }

        hideSupplierModal();
        loadSuppliers();
        updateSuppliersStats();
        updateStats();

    } catch (error) {
        showAlert('حدث خطأ أثناء حفظ المورد', 'error');
    }
}

// حذف مورد
function deleteSupplier(supplierId) {
    const supplier = suppliersData.find(s => s.id === supplierId);
    if (!supplier) return;

    // التحقق من وجود مشتريات للمورد
    const purchases = db.getData('purchases');
    const supplierPurchases = purchases.filter(purchase => purchase.supplierId === supplierId);

    if (supplierPurchases.length > 0) {
        showAlert('لا يمكن حذف المورد لوجود مشتريات مرتبطة به', 'warning');
        return;
    }

    showConfirm(`هل أنت متأكد من حذف المورد "${supplier.name}"؟`, function() {
        try {
            db.deleteItem('suppliers', supplierId);
            showAlert('تم حذف المورد بنجاح', 'success');
            loadSuppliers();
            updateSuppliersStats();
            updateStats();
        } catch (error) {
            showAlert('حدث خطأ أثناء حذف المورد', 'error');
        }
    });
}

// عرض تفاصيل المورد
function showSupplierDetails(supplierId) {
    const supplier = suppliersData.find(s => s.id === supplierId);
    if (!supplier) return;

    // الحصول على مشتريات المورد
    const purchases = db.getData('purchases').filter(purchase => purchase.supplierId === supplierId);
    const payments = db.getData('payments').filter(payment => payment.supplierId === supplierId);

    // حساب إجمالي المشتريات والدفعات
    const totalPurchases = purchases.reduce((sum, purchase) => sum + purchase.total, 0);
    const totalPayments = payments.reduce((sum, payment) => sum + payment.amount, 0);

    const balanceStatus = getSupplierBalanceStatus(supplier.balance);

    document.getElementById('supplierDetailsContent').innerHTML = `
        <div class="supplier-details">
            <div class="supplier-info-section">
                <h4>معلومات المورد</h4>
                <div class="detail-row">
                    <span class="detail-label">الاسم:</span>
                    <span class="detail-value">${supplier.name}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">الهاتف:</span>
                    <span class="detail-value">${supplier.phone || 'غير محدد'}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">البريد الإلكتروني:</span>
                    <span class="detail-value">${supplier.email || 'غير محدد'}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">العنوان:</span>
                    <span class="detail-value">${supplier.address || 'غير محدد'}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">الرصيد الحالي:</span>
                    <span class="detail-value">
                        <span class="balance-amount ${balanceStatus.class}">
                            ${formatCurrency(supplier.balance)}
                        </span>
                    </span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">تاريخ الإضافة:</span>
                    <span class="detail-value">${formatDateTime(supplier.createdAt)}</span>
                </div>
            </div>

            <div class="supplier-stats-section">
                <h4>إحصائيات المورد</h4>
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-label">إجمالي المشتريات</div>
                        <div class="stat-value">${formatCurrency(totalPurchases)}</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-label">إجمالي الدفعات</div>
                        <div class="stat-value">${formatCurrency(totalPayments)}</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-label">عدد الفواتير</div>
                        <div class="stat-value">${formatArabicNumber(purchases.length)}</div>
                    </div>
                </div>
            </div>

            <div class="supplier-transactions-section">
                <h4>آخر المعاملات</h4>
                <div class="transactions-list">
                    ${purchases.slice(-5).reverse().map(purchase => `
                        <div class="transaction-item">
                            <div class="transaction-info">
                                <span class="transaction-type">فاتورة مشتريات</span>
                                <span class="transaction-number">${purchase.invoiceNumber || 'غير محدد'}</span>
                            </div>
                            <div class="transaction-amount">${formatCurrency(purchase.total)}</div>
                            <div class="transaction-date">${formatDateTime(purchase.createdAt)}</div>
                        </div>
                    `).join('')}
                    ${payments.slice(-5).reverse().map(payment => `
                        <div class="transaction-item">
                            <div class="transaction-info">
                                <span class="transaction-type">دفعة</span>
                                <span class="transaction-notes">${payment.notes || ''}</span>
                            </div>
                            <div class="transaction-amount positive">${formatCurrency(payment.amount)}</div>
                            <div class="transaction-date">${formatDateTime(payment.createdAt)}</div>
                        </div>
                    `).join('')}
                    ${(purchases.length === 0 && payments.length === 0) ? '<div class="no-transactions">لا توجد معاملات</div>' : ''}
                </div>
            </div>
        </div>
    `;

    document.getElementById('supplierDetailsModal').classList.remove('hidden');
}

// إخفاء تفاصيل المورد
function hideSupplierDetailsModal() {
    document.getElementById('supplierDetailsModal').classList.add('hidden');
}

// عرض نافذة دفع للمورد
function showSupplierPaymentModal(supplierId) {
    const supplier = suppliersData.find(s => s.id === supplierId);
    if (!supplier) return;

    currentSupplierId = supplierId;
    document.getElementById('supplierPaymentForm').reset();
    document.getElementById('supplierPaymentModal').classList.remove('hidden');
}

// إخفاء نافذة دفع المورد
function hideSupplierPaymentModal() {
    document.getElementById('supplierPaymentModal').classList.add('hidden');
    currentSupplierId = null;
}

// حفظ دفعة المورد
function saveSupplierPayment() {
    const form = document.getElementById('supplierPaymentForm');
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    const amount = parseFloat(document.getElementById('supplierPaymentAmount').value);
    const notes = sanitizeInput(document.getElementById('supplierPaymentNotes').value);

    if (amount <= 0) {
        showAlert('مبلغ الدفعة يجب أن يكون أكبر من صفر', 'warning');
        return;
    }

    const paymentData = {
        supplierId: currentSupplierId,
        amount: amount,
        notes: notes,
        type: 'supplier_payment'
    };

    try {
        // حفظ الدفعة
        db.addItem('payments', paymentData);

        // تحديث رصيد المورد (تقليل المستحق للمورد)
        const supplier = db.getData('suppliers').find(s => s.id === currentSupplierId);
        if (supplier) {
            const newBalance = supplier.balance - amount;
            db.updateItem('suppliers', currentSupplierId, { balance: newBalance });
        }

        showAlert('تم إضافة الدفعة بنجاح', 'success');
        hideSupplierPaymentModal();
        loadSuppliers();
        updateSuppliersStats();
        updateStats();

    } catch (error) {
        showAlert('حدث خطأ أثناء إضافة الدفعة', 'error');
    }
}

// طباعة كشف حساب المورد
function printSupplierStatement() {
    const content = document.getElementById('supplierDetailsContent').innerHTML;
    printContent(content, 'كشف حساب المورد');
}

// تصدير الموردين
function exportSuppliers() {
    try {
        const suppliers = db.getData('suppliers');
        const csvContent = generateSuppliersCSV(suppliers);
        downloadCSV(csvContent, 'suppliers.csv');
        showAlert('تم تصدير الموردين بنجاح', 'success');
    } catch (error) {
        showAlert('حدث خطأ أثناء تصدير الموردين', 'error');
    }
}

// توليد ملف CSV للموردين
function generateSuppliersCSV(suppliers) {
    const headers = ['الاسم', 'الهاتف', 'البريد الإلكتروني', 'العنوان', 'الرصيد'];
    const rows = suppliers.map(supplier => [
        supplier.name,
        supplier.phone || '',
        supplier.email || '',
        supplier.address || '',
        supplier.balance
    ]);

    return [headers, ...rows].map(row => row.join(',')).join('\n');
}
