// إدارة الديون والمدفوعات
function getDebtsHTML() {
    return `
        <div class="debts-container">
            <div class="page-header">
                <h2 class="page-title">إدارة الديون والمدفوعات</h2>
                <div class="page-actions">
                    <button class="btn btn-success" onclick="showPaymentsHistory()">
                        <i class="fas fa-history"></i>
                        تاريخ المدفوعات
                    </button>
                    <button class="btn btn-info" onclick="exportDebtsReport()">
                        <i class="fas fa-download"></i>
                        تقرير الديون
                    </button>
                </div>
            </div>

            <!-- إحصائيات الديون -->
            <div class="debts-stats">
                <div class="stat-card debt-stat">
                    <div class="stat-icon debt">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="totalDebtsAmount">٠.٠٠ ريال</h3>
                        <p>إجمالي الديون</p>
                    </div>
                </div>
                
                <div class="stat-card customers-stat">
                    <div class="stat-icon customers">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="debtorCustomersCount">٠</h3>
                        <p>العملاء المدينون</p>
                    </div>
                </div>
                
                <div class="stat-card payments-stat">
                    <div class="stat-icon payments">
                        <i class="fas fa-money-bill-wave"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="todayPaymentsAmount">٠.٠٠ ريال</h3>
                        <p>مدفوعات اليوم</p>
                    </div>
                </div>
                
                <div class="stat-card overdue-stat">
                    <div class="stat-icon overdue">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="overdueDebtsAmount">٠.٠٠ ريال</h3>
                        <p>ديون متأخرة</p>
                    </div>
                </div>
            </div>

            <!-- شريط البحث والفلترة -->
            <div class="search-filter-bar">
                <div class="search-group">
                    <div class="input-group">
                        <input type="text" id="debtorSearch" placeholder="البحث في العملاء المدينين..." onkeyup="searchDebtors()">
                        <i class="fas fa-search"></i>
                    </div>
                </div>
                <div class="filter-group">
                    <select id="debtAmountFilter" onchange="filterDebtors()">
                        <option value="">جميع المبالغ</option>
                        <option value="small">أقل من ١٠٠٠ ريال</option>
                        <option value="medium">١٠٠٠ - ٥٠٠٠ ريال</option>
                        <option value="large">أكثر من ٥٠٠٠ ريال</option>
                    </select>
                </div>
            </div>

            <!-- جدول العملاء المدينين -->
            <div class="table-container">
                <table class="data-table" id="debtorsTable">
                    <thead>
                        <tr>
                            <th>اسم العميل</th>
                            <th>الهاتف</th>
                            <th>مبلغ الدين</th>
                            <th>آخر معاملة</th>
                            <th>عدد الأيام</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="debtorsTableBody">
                        <!-- سيتم ملء البيانات هنا -->
                    </tbody>
                </table>
            </div>

            <!-- تحليل الديون -->
            <div class="debts-analysis">
                <h3>تحليل الديون</h3>
                <div class="analysis-grid">
                    <div class="analysis-card">
                        <h4>توزيع الديون حسب المبلغ</h4>
                        <div class="debt-distribution">
                            <div class="distribution-item">
                                <span class="label">أقل من ١٠٠٠ ريال:</span>
                                <span class="value" id="smallDebtsCount">٠</span>
                                <span class="percentage" id="smallDebtsPercentage">٠%</span>
                            </div>
                            <div class="distribution-item">
                                <span class="label">١٠٠٠ - ٥٠٠٠ ريال:</span>
                                <span class="value" id="mediumDebtsCount">٠</span>
                                <span class="percentage" id="mediumDebtsPercentage">٠%</span>
                            </div>
                            <div class="distribution-item">
                                <span class="label">أكثر من ٥٠٠٠ ريال:</span>
                                <span class="value" id="largeDebtsCount">٠</span>
                                <span class="percentage" id="largeDebtsPercentage">٠%</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="analysis-card">
                        <h4>أكبر المدينين</h4>
                        <div id="topDebtorsList" class="top-debtors-list">
                            <!-- سيتم ملء القائمة هنا -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- نافذة تاريخ المدفوعات -->
        <div id="paymentsHistoryModal" class="modal hidden">
            <div class="modal-content large-modal">
                <div class="modal-header">
                    <h3>تاريخ المدفوعات</h3>
                    <button class="btn btn-icon" onclick="hidePaymentsHistoryModal()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="history-filters">
                        <div class="filter-group">
                            <input type="date" id="paymentsDateFrom" onchange="filterPaymentsHistory()">
                            <span>إلى</span>
                            <input type="date" id="paymentsDateTo" onchange="filterPaymentsHistory()">
                            <select id="paymentTypeFilter" onchange="filterPaymentsHistory()">
                                <option value="">جميع الأنواع</option>
                                <option value="customer_payment">دفعات العملاء</option>
                                <option value="supplier_payment">دفعات الموردين</option>
                            </select>
                        </div>
                    </div>
                    <div class="table-container">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>النوع</th>
                                    <th>العميل/المورد</th>
                                    <th>المبلغ</th>
                                    <th>التاريخ</th>
                                    <th>ملاحظات</th>
                                </tr>
                            </thead>
                            <tbody id="paymentsHistoryTableBody">
                                <!-- سيتم ملء البيانات هنا -->
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" onclick="hidePaymentsHistoryModal()">إغلاق</button>
                </div>
            </div>
        </div>

        <!-- نافذة تفاصيل دين العميل -->
        <div id="customerDebtModal" class="modal hidden">
            <div class="modal-content large-modal">
                <div class="modal-header">
                    <h3>تفاصيل دين العميل</h3>
                    <button class="btn btn-icon" onclick="hideCustomerDebtModal()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div id="customerDebtContent">
                        <!-- سيتم ملء التفاصيل هنا -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-success" onclick="showQuickPaymentModal()">
                        <i class="fas fa-money-bill"></i>
                        إضافة دفعة سريعة
                    </button>
                    <button class="btn btn-info" onclick="printCustomerDebtStatement()">
                        <i class="fas fa-print"></i>
                        طباعة كشف الحساب
                    </button>
                    <button class="btn btn-secondary" onclick="hideCustomerDebtModal()">إغلاق</button>
                </div>
            </div>
        </div>

        <!-- نافذة دفعة سريعة -->
        <div id="quickPaymentModal" class="modal hidden">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>دفعة سريعة</h3>
                    <button class="btn btn-icon" onclick="hideQuickPaymentModal()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="quickPaymentForm">
                        <div class="customer-debt-info">
                            <div class="info-row">
                                <span class="label">العميل:</span>
                                <span class="value" id="quickPaymentCustomerName"></span>
                            </div>
                            <div class="info-row">
                                <span class="label">إجمالي الدين:</span>
                                <span class="value debt-amount" id="quickPaymentDebtAmount"></span>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="quickPaymentAmount">مبلغ الدفعة *</label>
                            <input type="number" id="quickPaymentAmount" step="0.01" min="0" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="quickPaymentNotes">ملاحظات</label>
                            <textarea id="quickPaymentNotes" rows="3" placeholder="ملاحظات اختيارية..."></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" onclick="hideQuickPaymentModal()">إلغاء</button>
                    <button class="btn btn-primary" onclick="saveQuickPayment()">حفظ الدفعة</button>
                </div>
            </div>
        </div>
    `;
}

// متغيرات الديون
let debtorsData = [];
let currentDebtorCustomer = null;

// تهيئة صفحة الديون
function initializeDebts() {
    loadDebtors();
    updateDebtsStats();
    updateDebtsAnalysis();
}

// تحميل العملاء المدينين
function loadDebtors() {
    const customers = db.getData('customers');
    debtorsData = customers.filter(customer => customer.balance < 0 && customer.name !== 'ضيف');
    displayDebtors(debtorsData);
}

// عرض العملاء المدينين
function displayDebtors(debtors) {
    const tbody = document.getElementById('debtorsTableBody');

    if (debtors.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="6" class="text-center">لا توجد ديون</td>
            </tr>
        `;
        return;
    }

    // ترتيب حسب مبلغ الدين (الأكبر أولاً)
    const sortedDebtors = debtors.sort((a, b) => a.balance - b.balance);

    tbody.innerHTML = sortedDebtors.map(customer => {
        const debtAmount = Math.abs(customer.balance);
        const lastTransaction = getLastTransactionDate(customer.id);
        const daysSinceLastTransaction = lastTransaction ?
            Math.floor((new Date() - new Date(lastTransaction)) / (1000 * 60 * 60 * 24)) : 0;

        return `
            <tr>
                <td>
                    <div class="customer-info">
                        <strong>${customer.name}</strong>
                    </div>
                </td>
                <td>${customer.phone || '-'}</td>
                <td>
                    <span class="debt-amount">${formatCurrency(debtAmount)}</span>
                </td>
                <td>${lastTransaction ? formatDate(lastTransaction) : 'لا توجد معاملات'}</td>
                <td>
                    <span class="days-count ${daysSinceLastTransaction > 30 ? 'overdue' : ''}">${formatArabicNumber(daysSinceLastTransaction)}</span>
                </td>
                <td>
                    <div class="action-buttons">
                        <button class="btn btn-sm btn-info" onclick="showCustomerDebtDetails('${customer.id}')" title="تفاصيل الدين">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-success" onclick="showQuickPayment('${customer.id}')" title="دفعة سريعة">
                            <i class="fas fa-money-bill"></i>
                        </button>
                        <button class="btn btn-sm btn-warning" onclick="sendDebtReminder('${customer.id}')" title="إرسال تذكير">
                            <i class="fas fa-bell"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }).join('');
}

// الحصول على تاريخ آخر معاملة للعميل
function getLastTransactionDate(customerId) {
    const sales = db.getData('sales').filter(sale => sale.customerId === customerId);
    const payments = db.getData('payments').filter(payment => payment.customerId === customerId);

    const allTransactions = [...sales, ...payments];
    if (allTransactions.length === 0) return null;

    const sortedTransactions = allTransactions.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
    return sortedTransactions[0].createdAt;
}

// تحديث إحصائيات الديون
function updateDebtsStats() {
    const customers = db.getData('customers');
    const debtors = customers.filter(customer => customer.balance < 0);

    const totalDebts = debtors.reduce((sum, customer) => sum + Math.abs(customer.balance), 0);
    const debtorCount = debtors.length;

    // مدفوعات اليوم
    const today = new Date().toDateString();
    const payments = db.getData('payments');
    const todayPayments = payments
        .filter(payment => new Date(payment.createdAt).toDateString() === today)
        .reduce((sum, payment) => sum + payment.amount, 0);

    // الديون المتأخرة (أكثر من 30 يوم)
    const overdueDebts = debtors.filter(customer => {
        const lastTransaction = getLastTransactionDate(customer.id);
        if (!lastTransaction) return false;
        const daysSince = Math.floor((new Date() - new Date(lastTransaction)) / (1000 * 60 * 60 * 24));
        return daysSince > 30;
    }).reduce((sum, customer) => sum + Math.abs(customer.balance), 0);

    document.getElementById('totalDebtsAmount').textContent = formatCurrency(totalDebts);
    document.getElementById('debtorCustomersCount').textContent = formatArabicNumber(debtorCount);
    document.getElementById('todayPaymentsAmount').textContent = formatCurrency(todayPayments);
    document.getElementById('overdueDebtsAmount').textContent = formatCurrency(overdueDebts);
}

// تحديث تحليل الديون
function updateDebtsAnalysis() {
    const customers = db.getData('customers');
    const debtors = customers.filter(customer => customer.balance < 0);

    // توزيع الديون حسب المبلغ
    const smallDebts = debtors.filter(customer => Math.abs(customer.balance) < 1000);
    const mediumDebts = debtors.filter(customer => Math.abs(customer.balance) >= 1000 && Math.abs(customer.balance) <= 5000);
    const largeDebts = debtors.filter(customer => Math.abs(customer.balance) > 5000);

    const totalDebtors = debtors.length;

    document.getElementById('smallDebtsCount').textContent = formatArabicNumber(smallDebts.length);
    document.getElementById('mediumDebtsCount').textContent = formatArabicNumber(mediumDebts.length);
    document.getElementById('largeDebtsCount').textContent = formatArabicNumber(largeDebts.length);

    if (totalDebtors > 0) {
        document.getElementById('smallDebtsPercentage').textContent = formatArabicNumber(Math.round((smallDebts.length / totalDebtors) * 100)) + '%';
        document.getElementById('mediumDebtsPercentage').textContent = formatArabicNumber(Math.round((mediumDebts.length / totalDebtors) * 100)) + '%';
        document.getElementById('largeDebtsPercentage').textContent = formatArabicNumber(Math.round((largeDebts.length / totalDebtors) * 100)) + '%';
    }

    // أكبر المدينين
    const topDebtors = debtors
        .sort((a, b) => a.balance - b.balance)
        .slice(0, 5);

    const topDebtorsList = document.getElementById('topDebtorsList');
    if (topDebtors.length === 0) {
        topDebtorsList.innerHTML = '<div class="no-debtors">لا توجد ديون</div>';
    } else {
        topDebtorsList.innerHTML = topDebtors.map(customer => `
            <div class="top-debtor-item">
                <div class="debtor-info">
                    <span class="debtor-name">${customer.name}</span>
                    <span class="debtor-phone">${customer.phone || 'لا يوجد هاتف'}</span>
                </div>
                <div class="debtor-amount">${formatCurrency(Math.abs(customer.balance))}</div>
            </div>
        `).join('');
    }
}

// البحث في المدينين
function searchDebtors() {
    const searchTerm = document.getElementById('debtorSearch').value.toLowerCase();
    const filteredDebtors = debtorsData.filter(customer =>
        customer.name.toLowerCase().includes(searchTerm) ||
        (customer.phone && customer.phone.includes(searchTerm))
    );
    displayDebtors(filteredDebtors);
}

// فلترة المدينين
function filterDebtors() {
    const amountFilter = document.getElementById('debtAmountFilter').value;

    let filteredDebtors = debtorsData;

    if (amountFilter === 'small') {
        filteredDebtors = filteredDebtors.filter(customer => Math.abs(customer.balance) < 1000);
    } else if (amountFilter === 'medium') {
        filteredDebtors = filteredDebtors.filter(customer => Math.abs(customer.balance) >= 1000 && Math.abs(customer.balance) <= 5000);
    } else if (amountFilter === 'large') {
        filteredDebtors = filteredDebtors.filter(customer => Math.abs(customer.balance) > 5000);
    }

    displayDebtors(filteredDebtors);
}

// عرض تفاصيل دين العميل
function showCustomerDebtDetails(customerId) {
    const customer = db.getData('customers').find(c => c.id === customerId);
    if (!customer) return;

    currentDebtorCustomer = customer;

    // الحصول على معاملات العميل
    const sales = db.getData('sales').filter(sale => sale.customerId === customerId);
    const payments = db.getData('payments').filter(payment => payment.customerId === customerId);

    const debtAmount = Math.abs(customer.balance);
    const totalSales = sales.reduce((sum, sale) => sum + sale.total, 0);
    const totalPayments = payments.reduce((sum, payment) => sum + payment.amount, 0);

    document.getElementById('customerDebtContent').innerHTML = `
        <div class="customer-debt-details">
            <div class="debt-summary">
                <h4>ملخص الدين</h4>
                <div class="summary-grid">
                    <div class="summary-item">
                        <span class="label">اسم العميل:</span>
                        <span class="value">${customer.name}</span>
                    </div>
                    <div class="summary-item">
                        <span class="label">الهاتف:</span>
                        <span class="value">${customer.phone || 'غير محدد'}</span>
                    </div>
                    <div class="summary-item">
                        <span class="label">إجمالي الدين:</span>
                        <span class="value debt-amount">${formatCurrency(debtAmount)}</span>
                    </div>
                    <div class="summary-item">
                        <span class="label">إجمالي المبيعات:</span>
                        <span class="value">${formatCurrency(totalSales)}</span>
                    </div>
                    <div class="summary-item">
                        <span class="label">إجمالي المدفوعات:</span>
                        <span class="value">${formatCurrency(totalPayments)}</span>
                    </div>
                    <div class="summary-item">
                        <span class="label">عدد الفواتير:</span>
                        <span class="value">${formatArabicNumber(sales.length)}</span>
                    </div>
                </div>
            </div>

            <div class="debt-transactions">
                <h4>آخر المعاملات</h4>
                <div class="transactions-list">
                    ${sales.slice(-10).reverse().map(sale => `
                        <div class="transaction-item debt-transaction">
                            <div class="transaction-info">
                                <span class="transaction-type">فاتورة مبيعات</span>
                                <span class="transaction-number">${sale.invoiceNumber}</span>
                            </div>
                            <div class="transaction-amount negative">${formatCurrency(sale.total)}</div>
                            <div class="transaction-date">${formatDateTime(sale.createdAt)}</div>
                        </div>
                    `).join('')}
                    ${payments.slice(-10).reverse().map(payment => `
                        <div class="transaction-item payment-transaction">
                            <div class="transaction-info">
                                <span class="transaction-type">دفعة</span>
                                <span class="transaction-notes">${payment.notes || ''}</span>
                            </div>
                            <div class="transaction-amount positive">${formatCurrency(payment.amount)}</div>
                            <div class="transaction-date">${formatDateTime(payment.createdAt)}</div>
                        </div>
                    `).join('')}
                    ${(sales.length === 0 && payments.length === 0) ? '<div class="no-transactions">لا توجد معاملات</div>' : ''}
                </div>
            </div>
        </div>
    `;

    document.getElementById('customerDebtModal').classList.remove('hidden');
}

// إخفاء تفاصيل دين العميل
function hideCustomerDebtModal() {
    document.getElementById('customerDebtModal').classList.add('hidden');
    currentDebtorCustomer = null;
}

// عرض نافذة الدفعة السريعة
function showQuickPayment(customerId) {
    const customer = db.getData('customers').find(c => c.id === customerId);
    if (!customer) return;

    currentDebtorCustomer = customer;
    const debtAmount = Math.abs(customer.balance);

    document.getElementById('quickPaymentCustomerName').textContent = customer.name;
    document.getElementById('quickPaymentDebtAmount').textContent = formatCurrency(debtAmount);
    document.getElementById('quickPaymentForm').reset();

    document.getElementById('quickPaymentModal').classList.remove('hidden');
}

// عرض نافذة الدفعة السريعة من تفاصيل الدين
function showQuickPaymentModal() {
    if (!currentDebtorCustomer) return;

    hideCustomerDebtModal();
    showQuickPayment(currentDebtorCustomer.id);
}

// إخفاء نافذة الدفعة السريعة
function hideQuickPaymentModal() {
    document.getElementById('quickPaymentModal').classList.add('hidden');
    currentDebtorCustomer = null;
}

// حفظ الدفعة السريعة
function saveQuickPayment() {
    const form = document.getElementById('quickPaymentForm');
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    if (!currentDebtorCustomer) return;

    const amount = parseFloat(document.getElementById('quickPaymentAmount').value);
    const notes = sanitizeInput(document.getElementById('quickPaymentNotes').value);
    const debtAmount = Math.abs(currentDebtorCustomer.balance);

    if (amount <= 0) {
        showAlert('مبلغ الدفعة يجب أن يكون أكبر من صفر', 'warning');
        return;
    }

    if (amount > debtAmount) {
        showAlert('مبلغ الدفعة أكبر من إجمالي الدين', 'warning');
        return;
    }

    const paymentData = {
        customerId: currentDebtorCustomer.id,
        amount: amount,
        notes: notes,
        type: 'customer_payment'
    };

    try {
        // حفظ الدفعة
        db.addItem('payments', paymentData);

        // تحديث رصيد العميل
        const newBalance = currentDebtorCustomer.balance + amount;
        db.updateItem('customers', currentDebtorCustomer.id, { balance: newBalance });

        showAlert('تم إضافة الدفعة بنجاح', 'success');
        hideQuickPaymentModal();

        // إعادة تحميل البيانات
        loadDebtors();
        updateDebtsStats();
        updateDebtsAnalysis();
        updateStats();

    } catch (error) {
        showAlert('حدث خطأ أثناء إضافة الدفعة', 'error');
    }
}

// إرسال تذكير بالدين
function sendDebtReminder(customerId) {
    const customer = db.getData('customers').find(c => c.id === customerId);
    if (!customer) return;

    const debtAmount = Math.abs(customer.balance);
    const message = `تذكير: يرجى سداد المبلغ المستحق ${formatCurrency(debtAmount)} في أقرب وقت ممكن.`;

    // في التطبيق الحقيقي، يمكن إرسال رسالة نصية أو بريد إلكتروني
    showAlert(`تم إرسال تذكير للعميل ${customer.name}:\n${message}`, 'info', 'تذكير بالدين');
}

// عرض تاريخ المدفوعات
function showPaymentsHistory() {
    loadPaymentsHistory();
    document.getElementById('paymentsHistoryModal').classList.remove('hidden');
}

// إخفاء تاريخ المدفوعات
function hidePaymentsHistoryModal() {
    document.getElementById('paymentsHistoryModal').classList.add('hidden');
}

// تحميل تاريخ المدفوعات
function loadPaymentsHistory() {
    const payments = db.getData('payments');
    displayPaymentsHistory(payments);
}

// عرض تاريخ المدفوعات
function displayPaymentsHistory(payments) {
    const tbody = document.getElementById('paymentsHistoryTableBody');

    if (payments.length === 0) {
        tbody.innerHTML = '<tr><td colspan="5" class="text-center">لا توجد مدفوعات</td></tr>';
        return;
    }

    // ترتيب المدفوعات حسب التاريخ (الأحدث أولاً)
    const sortedPayments = payments.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

    tbody.innerHTML = sortedPayments.map(payment => {
        let entityName = 'غير محدد';

        if (payment.customerId) {
            const customer = db.getData('customers').find(c => c.id === payment.customerId);
            entityName = customer ? customer.name : 'عميل محذوف';
        } else if (payment.supplierId) {
            const supplier = db.getData('suppliers').find(s => s.id === payment.supplierId);
            entityName = supplier ? supplier.name : 'مورد محذوف';
        }

        return `
            <tr>
                <td>
                    <span class="payment-type ${payment.type}">
                        ${payment.type === 'customer_payment' ? 'دفعة عميل' : 'دفعة مورد'}
                    </span>
                </td>
                <td>${entityName}</td>
                <td>${formatCurrency(payment.amount)}</td>
                <td>${formatDateTime(payment.createdAt)}</td>
                <td>${payment.notes || '-'}</td>
            </tr>
        `;
    }).join('');
}

// فلترة تاريخ المدفوعات
function filterPaymentsHistory() {
    const dateFrom = document.getElementById('paymentsDateFrom').value;
    const dateTo = document.getElementById('paymentsDateTo').value;
    const typeFilter = document.getElementById('paymentTypeFilter').value;

    let payments = db.getData('payments');

    // فلترة حسب التاريخ
    if (dateFrom) {
        payments = payments.filter(payment => payment.createdAt >= dateFrom + 'T00:00:00');
    }

    if (dateTo) {
        payments = payments.filter(payment => payment.createdAt <= dateTo + 'T23:59:59');
    }

    // فلترة حسب النوع
    if (typeFilter) {
        payments = payments.filter(payment => payment.type === typeFilter);
    }

    displayPaymentsHistory(payments);
}

// طباعة كشف حساب العميل المدين
function printCustomerDebtStatement() {
    if (!currentDebtorCustomer) return;

    const content = document.getElementById('customerDebtContent').innerHTML;
    printContent(content, `كشف حساب العميل ${currentDebtorCustomer.name}`);
}

// تصدير تقرير الديون
function exportDebtsReport() {
    try {
        const customers = db.getData('customers');
        const debtors = customers.filter(customer => customer.balance < 0);

        const csvContent = generateDebtsReportCSV(debtors);
        downloadCSV(csvContent, 'debts-report.csv');
        showAlert('تم تصدير تقرير الديون بنجاح', 'success');
    } catch (error) {
        showAlert('حدث خطأ أثناء تصدير تقرير الديون', 'error');
    }
}

// توليد ملف CSV لتقرير الديون
function generateDebtsReportCSV(debtors) {
    const headers = ['اسم العميل', 'الهاتف', 'البريد الإلكتروني', 'مبلغ الدين', 'آخر معاملة', 'عدد الأيام'];
    const rows = debtors.map(customer => {
        const debtAmount = Math.abs(customer.balance);
        const lastTransaction = getLastTransactionDate(customer.id);
        const daysSinceLastTransaction = lastTransaction ?
            Math.floor((new Date() - new Date(lastTransaction)) / (1000 * 60 * 60 * 24)) : 0;

        return [
            customer.name,
            customer.phone || '',
            customer.email || '',
            debtAmount,
            lastTransaction ? new Date(lastTransaction).toLocaleDateString('ar-SA') : '',
            daysSinceLastTransaction
        ];
    });

    return [headers, ...rows].map(row => row.join(',')).join('\n');
}
