<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة نقاط البيع</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <!-- شاشة تسجيل الدخول والتسجيل -->
    <div id="loginScreen" class="auth-screen">
        <div class="auth-container">
            <!-- مفتاح الوضع المظلم/الفاتح -->
            <div class="theme-toggle">
                <button id="themeToggle" class="theme-btn">
                    <i class="fas fa-sun"></i>
                </button>
            </div>

            <!-- عنوان التطبيق -->
            <div class="auth-header">
                <h1>Modern Neumorphic</h1>
                <h2>Login & Signup Form</h2>
                <p>with Dark Mode Toggle</p>
            </div>

            <!-- أزرار التبديل بين Login و Sign Up -->
            <div class="auth-tabs">
                <button id="loginTab" class="auth-tab active">Login</button>
                <button id="signupTab" class="auth-tab">Sign Up</button>
            </div>

            <!-- نموذج تسجيل الدخول -->
            <div id="loginForm" class="auth-form active">
                <h3>Login</h3>

                <form id="loginFormElement">
                    <div class="form-group">
                        <div class="input-wrapper">
                            <i class="fas fa-envelope input-icon"></i>
                            <input type="email" id="loginEmail" placeholder="Email" required>
                        </div>
                    </div>

                    <div class="form-group">
                        <div class="input-wrapper">
                            <i class="fas fa-lock input-icon"></i>
                            <input type="password" id="loginPassword" placeholder="Password" required>
                            <button type="button" class="password-toggle" onclick="togglePassword('loginPassword')">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>

                    <button type="submit" class="auth-btn primary">Login</button>
                </form>

                <div class="auth-divider">
                    <span>Or continue with</span>
                </div>

                <div class="social-buttons">
                    <button class="social-btn facebook">
                        <i class="fab fa-facebook-f"></i>
                    </button>
                    <button class="social-btn google">
                        <i class="fab fa-google"></i>
                    </button>
                    <button class="social-btn github">
                        <i class="fab fa-github"></i>
                    </button>
                </div>
            </div>

            <!-- نموذج التسجيل -->
            <div id="signupForm" class="auth-form">
                <h3>Sign Up</h3>

                <form id="signupFormElement">
                    <div class="form-group">
                        <div class="input-wrapper">
                            <i class="fas fa-user input-icon"></i>
                            <input type="text" id="signupName" placeholder="Full Name" required>
                        </div>
                    </div>

                    <div class="form-group">
                        <div class="input-wrapper">
                            <i class="fas fa-envelope input-icon"></i>
                            <input type="email" id="signupEmail" placeholder="Email" required>
                        </div>
                    </div>

                    <div class="form-group">
                        <div class="input-wrapper">
                            <i class="fas fa-lock input-icon"></i>
                            <input type="password" id="signupPassword" placeholder="Password" required>
                            <button type="button" class="password-toggle" onclick="togglePassword('signupPassword')">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>

                    <div class="form-group">
                        <div class="input-wrapper">
                            <i class="fas fa-lock input-icon"></i>
                            <input type="password" id="confirmPassword" placeholder="Confirm Password" required>
                            <button type="button" class="password-toggle" onclick="togglePassword('confirmPassword')">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>

                    <button type="submit" class="auth-btn primary">Sign Up</button>
                </form>

                <div class="auth-divider">
                    <span>Or continue with</span>
                </div>

                <div class="social-buttons">
                    <button class="social-btn facebook">
                        <i class="fab fa-facebook-f"></i>
                    </button>
                    <button class="social-btn google">
                        <i class="fab fa-google"></i>
                    </button>
                    <button class="social-btn github">
                        <i class="fab fa-github"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- التطبيق الرئيسي -->
    <div id="mainApp" class="main-app hidden">
        <!-- الشريط الجانبي -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <i class="fas fa-store"></i>
                    <span>نقاط البيع</span>
                </div>
            </div>
            
            <nav class="sidebar-nav">
                <ul class="nav-menu">
                    <li class="nav-item active" data-page="dashboard">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>لوحة التحكم</span>
                    </li>
                    <li class="nav-item" data-page="sales">
                        <i class="fas fa-shopping-cart"></i>
                        <span>المبيعات</span>
                    </li>
                    <li class="nav-item" data-page="products">
                        <i class="fas fa-boxes"></i>
                        <span>المنتجات</span>
                    </li>
                    <li class="nav-item" data-page="categories">
                        <i class="fas fa-tags"></i>
                        <span>الأصناف</span>
                    </li>
                    <li class="nav-item" data-page="customers">
                        <i class="fas fa-users"></i>
                        <span>العملاء</span>
                    </li>
                    <li class="nav-item" data-page="suppliers">
                        <i class="fas fa-truck"></i>
                        <span>الموردين</span>
                    </li>
                    <li class="nav-item" data-page="purchases">
                        <i class="fas fa-shopping-bag"></i>
                        <span>المشتريات</span>
                    </li>
                    <li class="nav-item" data-page="debts">
                        <i class="fas fa-credit-card"></i>
                        <span>الديون</span>
                    </li>
                    <li class="nav-item" data-page="expenses">
                        <i class="fas fa-money-bill-wave"></i>
                        <span>المصروفات</span>
                    </li>
                    <li class="nav-item" data-page="reports">
                        <i class="fas fa-chart-bar"></i>
                        <span>التقارير</span>
                    </li>
                    <li class="nav-item" data-page="settings">
                        <i class="fas fa-cog"></i>
                        <span>الإعدادات</span>
                    </li>
                </ul>
            </nav>
            
            <div class="sidebar-footer">
                <button class="logout-btn" onclick="handleLogout()">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>تسجيل الخروج</span>
                </button>
            </div>
        </aside>

        <!-- المحتوى الرئيسي -->
        <main class="main-content">
            <header class="top-header">
                <div class="header-left">
                    <button class="btn btn-icon sidebar-toggle" onclick="toggleSidebar()">
                        <i class="fas fa-bars"></i>
                    </button>
                    <h1 id="pageTitle">لوحة التحكم</h1>
                </div>
                
                <div class="header-right">
                    <div class="header-stats">
                        <div class="stat-item">
                            <span class="stat-label">المبيعات اليوم</span>
                            <span class="stat-value" id="headerTodaySales">٠.٠٠ ريال</span>
                        </div>
                    </div>
                    
                    <div class="header-actions">
                        <button class="btn btn-icon" onclick="toggleTheme()" title="تبديل الثيم">
                            <i class="fas fa-moon"></i>
                        </button>
                        <button class="btn btn-icon" onclick="showNotifications()" title="الإشعارات">
                            <i class="fas fa-bell"></i>
                            <span class="notification-badge" id="notificationBadge">0</span>
                        </button>
                    </div>
                </div>
            </header>
            
            <div class="content-wrapper">
                <div id="pageContent">
                    <!-- سيتم تحميل محتوى الصفحات هنا -->
                </div>
            </div>
        </main>
    </div>

    <!-- نافذة التنبيه -->
    <div id="alertModal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="alertTitle">تنبيه</h3>
            </div>
            <div class="modal-body">
                <p id="alertMessage"></p>
            </div>
            <div class="modal-footer">
                <button class="btn btn-primary" onclick="hideAlertModal()">موافق</button>
            </div>
        </div>
    </div>

    <!-- نافذة التأكيد -->
    <div id="confirmModal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="confirmTitle">تأكيد</h3>
            </div>
            <div class="modal-body">
                <p id="confirmMessage"></p>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="hideConfirmModal()">إلغاء</button>
                <button class="btn btn-primary" onclick="handleConfirmYes()">موافق</button>
            </div>
        </div>
    </div>

    <script>
        // متغيرات عامة للمصادقة
        let currentTheme = localStorage.getItem('theme') || 'light';
        let currentForm = 'login';

        // تطبيق الثيم فوراً عند تحميل الصفحة
        (function() {
            document.documentElement.setAttribute('data-theme', currentTheme);
            if (currentTheme === 'dark') {
                document.documentElement.style.setProperty('--auth-bg', '#2d3748');
                document.documentElement.style.setProperty('--auth-card-bg', '#2d3748');
                document.documentElement.style.setProperty('--auth-text', '#e2e8f0');
                document.documentElement.style.setProperty('--auth-text-secondary', '#a0aec0');
                document.documentElement.style.setProperty('--auth-shadow-light', '#4a5568');
                document.documentElement.style.setProperty('--auth-shadow-dark', '#1a202c');
            }
        })();

        // إضافة تأثيرات الحركة
        document.addEventListener('DOMContentLoaded', function() {
            // تطبيق الثيم المحفوظ
            applyTheme();

            // إعداد مستمعي الأحداث للمصادقة
            setupAuthEventListeners();

            // التحقق من حالة تسجيل الدخول
            const isLoggedIn = sessionStorage.getItem('erp_logged_in');

            if (isLoggedIn) {
                document.getElementById('loginScreen').classList.add('hidden');
                document.getElementById('mainApp').classList.remove('hidden');
            } else {
                document.getElementById('loginScreen').classList.remove('hidden');
                document.getElementById('mainApp').classList.add('hidden');
            }

            const cards = document.querySelectorAll('.stat-card');
            cards.forEach((card, index) => {
                setTimeout(() => {
                    card.classList.add('fade-in');
                }, index * 100);
            });

            // التحكم في الشريط الجانبي للشاشات الصغيرة
            const sidebarToggle = document.querySelector('.sidebar-toggle');
            const sidebar = document.querySelector('.sidebar');
            const mainContent = document.querySelector('.main-content');

            if (sidebarToggle) {
                sidebarToggle.addEventListener('click', function() {
                    sidebar.classList.toggle('open');
                });

                // إغلاق الشريط الجانبي عند النقر خارجه
                if (mainContent) {
                    mainContent.addEventListener('click', function() {
                        if (sidebar.classList.contains('open')) {
                            sidebar.classList.remove('open');
                        }
                    });
                }
            }
        });

        // إعداد مستمعي الأحداث للمصادقة
        function setupAuthEventListeners() {
            // مفتاح الوضع المظلم/الفاتح
            const themeToggle = document.getElementById('themeToggle');
            if (themeToggle) {
                themeToggle.addEventListener('click', toggleAuthTheme);
                // تحديث أيقونة الثيم عند الإعداد
                const themeIcon = themeToggle.querySelector('i');
                if (themeIcon) {
                    themeIcon.className = currentTheme === 'light' ? 'fas fa-moon' : 'fas fa-sun';
                }
            }

            // أزرار التبديل بين النماذج
            const loginTab = document.getElementById('loginTab');
            const signupTab = document.getElementById('signupTab');

            if (loginTab) {
                loginTab.addEventListener('click', () => switchForm('login'));
            }
            if (signupTab) {
                signupTab.addEventListener('click', () => switchForm('signup'));
            }

            // نماذج المصادقة
            const loginFormElement = document.getElementById('loginFormElement');
            const signupFormElement = document.getElementById('signupFormElement');

            if (loginFormElement) {
                console.log('Adding login form listener'); // للتشخيص
                loginFormElement.addEventListener('submit', handleLoginSubmit);
            } else {
                console.error('Login form element not found');
            }

            if (signupFormElement) {
                console.log('Adding signup form listener'); // للتشخيص
                signupFormElement.addEventListener('submit', handleSignupSubmit);
            } else {
                console.error('Signup form element not found');
            }

            // إضافة معالجات أحداث مباشرة للأزرار كبديل
            const loginBtn = document.querySelector('#loginForm .auth-btn');
            const signupBtn = document.querySelector('#signupForm .auth-btn');

            if (loginBtn) {
                loginBtn.addEventListener('click', function(e) {
                    if (e.target.type === 'submit') {
                        e.preventDefault();
                        handleLoginSubmit(e);
                    }
                });
            }

            if (signupBtn) {
                signupBtn.addEventListener('click', function(e) {
                    if (e.target.type === 'submit') {
                        e.preventDefault();
                        handleSignupSubmit(e);
                    }
                });
            }
        }

        // تبديل الثيم للمصادقة
        function toggleAuthTheme() {
            currentTheme = currentTheme === 'light' ? 'dark' : 'light';
            localStorage.setItem('theme', currentTheme);
            applyTheme();

            // إجبار إعادة رسم الصفحة لضمان التطبيق
            setTimeout(() => {
                document.body.style.display = 'none';
                document.body.offsetHeight; // trigger reflow
                document.body.style.display = '';
            }, 10);
        }

        // تطبيق الثيم
        function applyTheme() {
            // تطبيق الثيم على العنصر الجذر
            document.documentElement.setAttribute('data-theme', currentTheme);

            // تحديث أيقونة الثيم
            const themeIcon = document.querySelector('#themeToggle i');
            if (themeIcon) {
                themeIcon.className = currentTheme === 'light' ? 'fas fa-moon' : 'fas fa-sun';
            }

            // إضافة فئة CSS للجسم لضمان التطبيق
            document.body.className = document.body.className.replace(/theme-\w+/g, '');
            document.body.classList.add('theme-' + currentTheme);

            // تحديث متغيرات CSS مباشرة للتأكد
            const root = document.documentElement;
            if (currentTheme === 'dark') {
                root.style.setProperty('--auth-bg', '#2d3748');
                root.style.setProperty('--auth-card-bg', '#2d3748');
                root.style.setProperty('--auth-text', '#e2e8f0');
                root.style.setProperty('--auth-text-secondary', '#a0aec0');
                root.style.setProperty('--auth-shadow-light', '#4a5568');
                root.style.setProperty('--auth-shadow-dark', '#1a202c');
            } else {
                root.style.setProperty('--auth-bg', '#e0e5ec');
                root.style.setProperty('--auth-card-bg', '#e0e5ec');
                root.style.setProperty('--auth-text', '#333');
                root.style.setProperty('--auth-text-secondary', '#666');
                root.style.setProperty('--auth-shadow-light', '#ffffff');
                root.style.setProperty('--auth-shadow-dark', '#a3b1c6');
            }
        }

        // التبديل بين النماذج
        function switchForm(formType) {
            console.log('Switching to form:', formType); // للتشخيص
            currentForm = formType;

            // تحديث الأزرار
            document.querySelectorAll('.auth-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            const activeTab = document.getElementById(formType + 'Tab');
            if (activeTab) {
                activeTab.classList.add('active');
            } else {
                console.error('Tab not found:', formType + 'Tab');
            }

            // تحديث النماذج
            document.querySelectorAll('.auth-form').forEach(form => {
                form.classList.remove('active');
            });
            const activeForm = document.getElementById(formType + 'Form');
            if (activeForm) {
                activeForm.classList.add('active');
            } else {
                console.error('Form not found:', formType + 'Form');
            }
        }

        // تبديل رؤية كلمة المرور
        function togglePassword(inputId) {
            console.log('Toggling password for:', inputId); // للتشخيص
            const input = document.getElementById(inputId);

            if (!input) {
                console.error('Input not found:', inputId);
                return;
            }

            const icon = input.parentElement.querySelector('.password-toggle i');

            if (!icon) {
                console.error('Icon not found for input:', inputId);
                return;
            }

            if (input.type === 'password') {
                input.type = 'text';
                icon.className = 'fas fa-eye-slash';
            } else {
                input.type = 'password';
                icon.className = 'fas fa-eye';
            }
        }

        // معالجة تسجيل الدخول
        function handleLoginSubmit(e) {
            e.preventDefault();

            const email = document.getElementById('loginEmail').value;
            const password = document.getElementById('loginPassword').value;

            console.log('Login attempt:', { email, password }); // للتشخيص

            // التحقق من كلمة المرور (admin هي كلمة المرور الافتراضية)
            const isValidPassword = password === 'admin' || (db && db.verifyPassword && db.verifyPassword(password));

            if (isValidPassword) {
                sessionStorage.setItem('erp_logged_in', 'true');
                document.getElementById('loginScreen').classList.add('hidden');
                document.getElementById('mainApp').classList.remove('hidden');

                if (typeof showAlert === 'function') {
                    showAlert('مرحباً بك في نظام إدارة نقاط البيع', 'success');
                } else {
                    alert('مرحباً بك في نظام إدارة نقاط البيع');
                }

                if (typeof loadPage === 'function') {
                    loadPage('dashboard');
                }
            } else {
                if (typeof showAlert === 'function') {
                    showAlert('كلمة المرور غير صحيحة. استخدم: admin', 'error');
                } else {
                    alert('كلمة المرور غير صحيحة. استخدم: admin');
                }
                document.getElementById('loginPassword').value = '';
            }
        }

        // معالجة التسجيل
        function handleSignupSubmit(e) {
            e.preventDefault();

            const name = document.getElementById('signupName').value;
            const email = document.getElementById('signupEmail').value;
            const password = document.getElementById('signupPassword').value;
            const confirmPassword = document.getElementById('confirmPassword').value;

            console.log('Signup attempt:', { name, email, password, confirmPassword }); // للتشخيص

            // التحقق من أن جميع الحقول مملوءة
            if (!name || !email || !password || !confirmPassword) {
                if (typeof showAlert === 'function') {
                    showAlert('يرجى ملء جميع الحقول', 'error');
                } else {
                    alert('يرجى ملء جميع الحقول');
                }
                return;
            }

            // التحقق من تطابق كلمات المرور
            if (password !== confirmPassword) {
                if (typeof showAlert === 'function') {
                    showAlert('كلمات المرور غير متطابقة', 'error');
                } else {
                    alert('كلمات المرور غير متطابقة');
                }
                return;
            }

            // التحقق من طول كلمة المرور
            if (password.length < 4) {
                if (typeof showAlert === 'function') {
                    showAlert('كلمة المرور يجب أن تكون 4 أحرف على الأقل', 'error');
                } else {
                    alert('كلمة المرور يجب أن تكون 4 أحرف على الأقل');
                }
                return;
            }

            // في التطبيق الحقيقي، هنا سيتم إرسال البيانات للخادم
            if (typeof showAlert === 'function') {
                showAlert('تم إنشاء الحساب بنجاح! يمكنك الآن تسجيل الدخول بكلمة المرور: admin', 'success');
            } else {
                alert('تم إنشاء الحساب بنجاح! يمكنك الآن تسجيل الدخول بكلمة المرور: admin');
            }

            // التبديل إلى نموذج تسجيل الدخول
            switchForm('login');

            // مسح النموذج
            document.getElementById('signupFormElement').reset();
        }

        // دالة تسجيل الخروج
        function handleLogout() {
            if (typeof showConfirm === 'function') {
                showConfirm('هل أنت متأكد من تسجيل الخروج؟', function() {
                    sessionStorage.removeItem('erp_logged_in');
                    document.getElementById('loginScreen').classList.remove('hidden');
                    document.getElementById('mainApp').classList.add('hidden');
                    // مسح النماذج
                    if (document.getElementById('loginFormElement')) {
                        document.getElementById('loginFormElement').reset();
                    }
                    if (document.getElementById('signupFormElement')) {
                        document.getElementById('signupFormElement').reset();
                    }
                });
            } else {
                // إذا لم تكن دالة showConfirm متاحة، استخدم confirm العادي
                if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                    sessionStorage.removeItem('erp_logged_in');
                    document.getElementById('loginScreen').classList.remove('hidden');
                    document.getElementById('mainApp').classList.add('hidden');
                    // مسح النماذج
                    if (document.getElementById('loginFormElement')) {
                        document.getElementById('loginFormElement').reset();
                    }
                    if (document.getElementById('signupFormElement')) {
                        document.getElementById('signupFormElement').reset();
                    }
                }
            }
        }
    </script>

    <!-- تحميل ملفات JavaScript -->
    <script src="database.js"></script>
    <script src="dashboard.js"></script>
    <script src="products.js"></script>
    <script src="categories.js"></script>
    <script src="sales.js"></script>
    <script src="customers.js"></script>
    <script src="suppliers.js"></script>
    <script src="purchases.js"></script>
    <script src="debts.js"></script>
    <script src="expenses.js"></script>
    <script src="reports.js"></script>
    <script src="settings.js"></script>
    <script src="main.js"></script>
</body>
</html>
