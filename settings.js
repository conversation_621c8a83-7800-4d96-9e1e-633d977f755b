// إعدادات النظام
function getSettingsHTML() {
    return `
        <div class="settings-container">
            <div class="page-header">
                <h2 class="page-title">إعدادات النظام</h2>
                <div class="page-actions">
                    <button class="btn btn-success" onclick="saveAllSettings()">
                        <i class="fas fa-save"></i>
                        حفظ جميع الإعدادات
                    </button>
                    <button class="btn btn-warning" onclick="resetSettings()">
                        <i class="fas fa-undo"></i>
                        إعادة تعيين
                    </button>
                </div>
            </div>

            <!-- تبويبات الإعدادات -->
            <div class="settings-tabs">
                <button class="settings-tab-btn active" onclick="showSettingsTab('company')">
                    <i class="fas fa-building"></i>
                    معلومات الشركة
                </button>
                <button class="settings-tab-btn" onclick="showSettingsTab('system')">
                    <i class="fas fa-cogs"></i>
                    إعدادات النظام
                </button>
                <button class="settings-tab-btn" onclick="showSettingsTab('appearance')">
                    <i class="fas fa-palette"></i>
                    المظهر
                </button>
                <button class="settings-tab-btn" onclick="showSettingsTab('security')">
                    <i class="fas fa-lock"></i>
                    الأمان
                </button>
                <button class="settings-tab-btn" onclick="showSettingsTab('tools')">
                    <i class="fas fa-tools"></i>
                    أدوات متقدمة
                </button>
            </div>

            <!-- معلومات الشركة -->
            <div id="companySettings" class="settings-tab active">
                <div class="settings-section">
                    <h3>معلومات الشركة</h3>
                    <form id="companyForm">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="companyName">اسم الشركة *</label>
                                <input type="text" id="companyName" required>
                            </div>
                            <div class="form-group">
                                <label for="companyPhone">رقم الهاتف</label>
                                <input type="tel" id="companyPhone">
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="companyEmail">البريد الإلكتروني</label>
                                <input type="email" id="companyEmail">
                            </div>
                            <div class="form-group">
                                <label for="companyWebsite">الموقع الإلكتروني</label>
                                <input type="url" id="companyWebsite">
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="companyAddress">العنوان</label>
                            <textarea id="companyAddress" rows="3"></textarea>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="taxNumber">الرقم الضريبي</label>
                                <input type="text" id="taxNumber">
                            </div>
                            <div class="form-group">
                                <label for="commercialRegister">السجل التجاري</label>
                                <input type="text" id="commercialRegister">
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="companyLogo">شعار الشركة</label>
                            <input type="file" id="companyLogo" accept="image/*" onchange="previewLogo(this)">
                            <div id="logoPreview" class="logo-preview">
                                <!-- سيتم عرض الشعار هنا -->
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- إعدادات النظام -->
            <div id="systemSettings" class="settings-tab hidden">
                <div class="settings-section">
                    <h3>إعدادات النظام</h3>
                    <form id="systemForm">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="currency">العملة</label>
                                <select id="currency">
                                    <option value="AED"> ريال يمني (ر.ي)</option>
                                    <option value="SAR">ريال سعودي (ر.س)</option>
                                    <option value="USD">دولار أمريكي ($)</option>
                                    <option value="EUR">يورو (€)</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="taxRate">معدل الضريبة (%)</label>
                                <input type="number" id="taxRate" min="0" max="100" step="0.01" value="15">
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="lowStockThreshold">حد التنبيه للمخزون المنخفض</label>
                                <input type="number" id="lowStockThreshold" min="1" value="10">
                            </div>
                            <div class="form-group">
                                <label for="invoicePrefix">بادئة رقم الفاتورة</label>
                                <input type="text" id="invoicePrefix" value="INV" maxlength="5">
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="autoBackup">
                                <span class="checkmark"></span>
                                تفعيل النسخ الاحتياطي التلقائي
                            </label>
                        </div>
                        
                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="lowStockAlerts">
                                <span class="checkmark"></span>
                                تنبيهات المخزون المنخفض
                            </label>
                        </div>
                        
                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="soundNotifications">
                                <span class="checkmark"></span>
                                التنبيهات الصوتية
                            </label>
                        </div>
                    </form>
                </div>
            </div>

            <!-- إعدادات المظهر -->
            <div id="appearanceSettings" class="settings-tab hidden">
                <div class="settings-section">
                    <h3>إعدادات المظهر</h3>
                    <form id="appearanceForm">
                        <div class="form-group">
                            <label for="theme">السمة</label>
                            <select id="theme" onchange="changeTheme(this.value)">
                                <option value="light">فاتح</option>
                                <option value="dark">داكن</option>
                                <option value="auto">تلقائي</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="fontSize">حجم الخط</label>
                            <select id="fontSize" onchange="changeFontSize(this.value)">
                                <option value="small">صغير</option>
                                <option value="medium">متوسط</option>
                                <option value="large">كبير</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="primaryColor">اللون الأساسي</label>
                            <input type="color" id="primaryColor" value="#667eea" onchange="changePrimaryColor(this.value)">
                        </div>
                        
                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="compactMode">
                                <span class="checkmark"></span>
                                الوضع المضغوط
                            </label>
                        </div>
                        
                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="showAnimations">
                                <span class="checkmark"></span>
                                عرض الحركات والانتقالات
                            </label>
                        </div>
                    </form>
                </div>
            </div>

            <!-- إعدادات الأمان -->
            <div id="securitySettings" class="settings-tab hidden">
                <div class="settings-section">
                    <div class="security-card">
                        <div class="security-header">
                            <i class="fas fa-key security-icon"></i>
                            <h3>تغيير كلمة المرور</h3>
                        </div>

                        <form id="passwordChangeForm" class="password-form">
                            <div class="password-fields">
                                <div class="form-group">
                                    <label for="currentPassword">كلمة المرور الحالية</label>
                                    <div class="password-input-group">
                                        <input type="password" id="currentPassword" required placeholder="أدخل كلمة المرور الحالية">
                                        <button type="button" class="password-toggle" onclick="togglePasswordVisibility('currentPassword')">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="newPassword">كلمة المرور الجديدة</label>
                                    <div class="password-input-group">
                                        <input type="password" id="newPassword" required placeholder="أدخل كلمة المرور الجديدة">
                                        <button type="button" class="password-toggle" onclick="togglePasswordVisibility('newPassword')">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="confirmPassword">تأكيد كلمة المرور</label>
                                    <div class="password-input-group">
                                        <input type="password" id="confirmPassword" required placeholder="أعد إدخال كلمة المرور الجديدة">
                                        <button type="button" class="password-toggle" onclick="togglePasswordVisibility('confirmPassword')">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <div class="password-strength">
                                <div class="strength-meter">
                                    <div class="strength-bar" id="strengthBar"></div>
                                </div>
                                <span class="strength-text" id="strengthText">قوة كلمة المرور</span>
                            </div>

                            <div class="password-requirements">
                                <h4>متطلبات كلمة المرور:</h4>
                                <ul>
                                    <li id="req-length">على الأقل 6 أحرف</li>
                                    <li id="req-number">رقم واحد على الأقل</li>
                                    <li id="req-letter">حرف واحد على الأقل</li>
                                </ul>
                            </div>

                            <div class="form-actions">
                                <button type="button" class="btn btn-primary" onclick="changePassword()">
                                    <i class="fas fa-key"></i>
                                    تغيير كلمة المرور
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- أدوات متقدمة -->
            <div id="toolsSettings" class="settings-tab hidden">
                <div class="settings-section">
                    <div class="tools-header">
                        <i class="fas fa-exclamation-triangle tools-warning-icon"></i>
                        <h3>أدوات متقدمة</h3>
                        <p class="tools-warning">تحذير: هذه الأدوات قد تؤثر على بياناتك. يرجى التأكد قبل الاستخدام.</p>
                    </div>

                    <div class="tools-grid">
                        <!-- حذف المنتجات -->
                        <div class="tool-card">
                            <div class="tool-icon">
                                <i class="fas fa-box"></i>
                            </div>
                            <div class="tool-content">
                                <h4>حذف المنتجات</h4>
                                <p>حذف جميع المنتجات من النظام</p>
                                <button class="btn btn-danger" onclick="deleteAllProducts()">
                                    <i class="fas fa-trash"></i>
                                    حذف المنتجات
                                </button>
                            </div>
                        </div>

                        <!-- حذف المصروفات -->
                        <div class="tool-card">
                            <div class="tool-icon">
                                <i class="fas fa-money-bill-wave"></i>
                            </div>
                            <div class="tool-content">
                                <h4>حذف المصروفات</h4>
                                <p>حذف جميع المصروفات من النظام</p>
                                <button class="btn btn-danger" onclick="deleteAllExpenses()">
                                    <i class="fas fa-trash"></i>
                                    حذف المصروفات
                                </button>
                            </div>
                        </div>

                        <!-- حذف العملاء -->
                        <div class="tool-card">
                            <div class="tool-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="tool-content">
                                <h4>حذف العملاء</h4>
                                <p>حذف جميع العملاء من النظام</p>
                                <button class="btn btn-danger" onclick="deleteAllCustomers()">
                                    <i class="fas fa-trash"></i>
                                    حذف العملاء
                                </button>
                            </div>
                        </div>

                        <!-- حذف المبيعات -->
                        <div class="tool-card">
                            <div class="tool-icon">
                                <i class="fas fa-shopping-cart"></i>
                            </div>
                            <div class="tool-content">
                                <h4>حذف المبيعات</h4>
                                <p>حذف جميع المبيعات من النظام</p>
                                <button class="btn btn-danger" onclick="deleteAllSales()">
                                    <i class="fas fa-trash"></i>
                                    حذف المبيعات
                                </button>
                            </div>
                        </div>

                        <!-- إنشاء نسخة احتياطية -->
                        <div class="tool-card">
                            <div class="tool-icon">
                                <i class="fas fa-download"></i>
                            </div>
                            <div class="tool-content">
                                <h4>إنشاء نسخة احتياطية</h4>
                                <p>تحميل نسخة احتياطية من البيانات</p>
                                <button class="btn btn-primary" onclick="exportBackup()">
                                    <i class="fas fa-download"></i>
                                    إنشاء نسخة احتياطية
                                </button>
                            </div>
                        </div>

                        <!-- استعادة نسخة احتياطية -->
                        <div class="tool-card">
                            <div class="tool-icon">
                                <i class="fas fa-upload"></i>
                            </div>
                            <div class="tool-content">
                                <h4>استعادة نسخة احتياطية</h4>
                                <p>استعادة البيانات من ملف</p>
                                <input type="file" id="restoreFileInput" accept=".json" style="display: none;" onchange="importBackup(this)">
                                <button class="btn btn-warning" onclick="document.getElementById('restoreFileInput').click()">
                                    <i class="fas fa-upload"></i>
                                    استعادة نسخة احتياطية
                                </button>
                            </div>
                        </div>

                        <!-- إعادة ضبط الإعدادات -->
                        <div class="tool-card">
                            <div class="tool-icon">
                                <i class="fas fa-cog"></i>
                            </div>
                            <div class="tool-content">
                                <h4>إعادة ضبط الإعدادات</h4>
                                <p>إعادة تعيين جميع الإعدادات</p>
                                <button class="btn btn-secondary" onclick="resetAllSettings()">
                                    <i class="fas fa-undo"></i>
                                    إعادة ضبط الإعدادات
                                </button>
                            </div>
                        </div>

                        <!-- مسح بيانات النظام -->
                        <div class="tool-card danger-card">
                            <div class="tool-icon">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="tool-content">
                                <h4>مسح بيانات النظام</h4>
                                <p>حذف جميع البيانات نهائياً</p>
                                <button class="btn btn-danger" onclick="clearAllSystemData()">
                                    <i class="fas fa-trash-alt"></i>
                                    مسح بيانات النظام
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
}

// تهيئة صفحة الإعدادات
function initializeSettings() {
    loadSettings();
}

// تحميل الإعدادات
function loadSettings() {
    try {
        const settings = db.getSettings();

        // معلومات الشركة
        const companyName = document.getElementById('companyName');
        if (companyName) companyName.value = settings.companyName || '';

        const companyPhone = document.getElementById('companyPhone');
        if (companyPhone) companyPhone.value = settings.companyPhone || '';

        const companyEmail = document.getElementById('companyEmail');
        if (companyEmail) companyEmail.value = settings.companyEmail || '';

        const companyWebsite = document.getElementById('companyWebsite');
        if (companyWebsite) companyWebsite.value = settings.companyWebsite || '';

        const companyAddress = document.getElementById('companyAddress');
        if (companyAddress) companyAddress.value = settings.companyAddress || '';

        const taxNumber = document.getElementById('taxNumber');
        if (taxNumber) taxNumber.value = settings.taxNumber || '';

        const commercialRegister = document.getElementById('commercialRegister');
        if (commercialRegister) commercialRegister.value = settings.commercialRegister || '';

        // إعدادات النظام
        const currency = document.getElementById('currency');
        if (currency) currency.value = settings.currency || 'ريال';

        const taxRate = document.getElementById('taxRate');
        if (taxRate) taxRate.value = settings.taxRate || 15;

        const lowStockThreshold = document.getElementById('lowStockThreshold');
        if (lowStockThreshold) lowStockThreshold.value = settings.lowStockThreshold || 10;

        const invoicePrefix = document.getElementById('invoicePrefix');
        if (invoicePrefix) invoicePrefix.value = settings.invoicePrefix || 'INV';

        const autoBackup = document.getElementById('autoBackup');
        if (autoBackup) autoBackup.checked = settings.autoBackup || false;

        const lowStockAlerts = document.getElementById('lowStockAlerts');
        if (lowStockAlerts) lowStockAlerts.checked = settings.lowStockAlerts !== false;

        const soundNotifications = document.getElementById('soundNotifications');
        if (soundNotifications) soundNotifications.checked = settings.soundNotifications !== false;

        // إعدادات المظهر
        const theme = document.getElementById('theme');
        if (theme) theme.value = settings.theme || 'light';

        const fontSize = document.getElementById('fontSize');
        if (fontSize) fontSize.value = settings.fontSize || 'medium';

        const primaryColor = document.getElementById('primaryColor');
        if (primaryColor) primaryColor.value = settings.primaryColor || '#667eea';

        const compactMode = document.getElementById('compactMode');
        if (compactMode) compactMode.checked = settings.compactMode || false;

        const showAnimations = document.getElementById('showAnimations');
        if (showAnimations) showAnimations.checked = settings.showAnimations !== false;

        // عرض الشعار إذا كان موجوداً
        const logoPreview = document.getElementById('logoPreview');
        if (settings.companyLogo && logoPreview) {
            logoPreview.innerHTML = `
                <img src="${settings.companyLogo}" alt="شعار الشركة" class="logo-image">
            `;
        }

        // تطبيق الإعدادات على الواجهة
        applySettings(settings);

    } catch (error) {
        console.error('خطأ في تحميل الإعدادات:', error);
        showAlert('حدث خطأ أثناء تحميل الإعدادات', 'error');
    }
}

// عرض تبويب الإعدادات
function showSettingsTab(tabName) {
    // إخفاء جميع التبويبات
    document.querySelectorAll('.settings-tab').forEach(tab => {
        tab.classList.add('hidden');
    });

    // إزالة الفئة النشطة من جميع الأزرار
    document.querySelectorAll('.settings-tab-btn').forEach(btn => {
        btn.classList.remove('active');
    });

    // عرض التبويب المحدد
    document.getElementById(tabName + 'Settings').classList.remove('hidden');

    // إضافة الفئة النشطة للزر المحدد
    document.querySelector(`[onclick="showSettingsTab('${tabName}')"]`).classList.add('active');
}

// معاينة الشعار
function previewLogo(input) {
    const file = input.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            document.getElementById('logoPreview').innerHTML = `
                <img src="${e.target.result}" alt="شعار الشركة" class="logo-image">
            `;
        };
        reader.readAsDataURL(file);
    }
}

// تغيير السمة
function changeTheme(theme) {
    document.documentElement.setAttribute('data-theme', theme);

    if (theme === 'auto') {
        const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
        document.documentElement.setAttribute('data-theme', prefersDark ? 'dark' : 'light');
    }
}

// تغيير حجم الخط
function changeFontSize(size) {
    document.documentElement.setAttribute('data-font-size', size);
}

// تغيير اللون الأساسي
function changePrimaryColor(color) {
    document.documentElement.style.setProperty('--primary-color', color);
}

// تنظيف المدخلات
function sanitizeInput(input) {
    if (!input) return '';
    return input.toString().trim();
}

// حفظ جميع الإعدادات
function saveAllSettings() {
    try {
        // الحصول على الإعدادات الحالية كنقطة بداية
        const currentSettings = db.getSettings();
        const settings = { ...currentSettings };

        // معلومات الشركة
        const companyName = document.getElementById('companyName');
        if (companyName) settings.companyName = sanitizeInput(companyName.value);

        const companyPhone = document.getElementById('companyPhone');
        if (companyPhone) settings.companyPhone = sanitizeInput(companyPhone.value);

        const companyEmail = document.getElementById('companyEmail');
        if (companyEmail) settings.companyEmail = sanitizeInput(companyEmail.value);

        const companyWebsite = document.getElementById('companyWebsite');
        if (companyWebsite) settings.companyWebsite = sanitizeInput(companyWebsite.value);

        const companyAddress = document.getElementById('companyAddress');
        if (companyAddress) settings.companyAddress = sanitizeInput(companyAddress.value);

        const taxNumber = document.getElementById('taxNumber');
        if (taxNumber) settings.taxNumber = sanitizeInput(taxNumber.value);

        const commercialRegister = document.getElementById('commercialRegister');
        if (commercialRegister) settings.commercialRegister = sanitizeInput(commercialRegister.value);

        // إعدادات النظام
        const currency = document.getElementById('currency');
        if (currency) settings.currency = currency.value;

        const taxRate = document.getElementById('taxRate');
        if (taxRate) settings.taxRate = parseFloat(taxRate.value) || 15;

        const lowStockThreshold = document.getElementById('lowStockThreshold');
        if (lowStockThreshold) settings.lowStockThreshold = parseInt(lowStockThreshold.value) || 10;

        const invoicePrefix = document.getElementById('invoicePrefix');
        if (invoicePrefix) settings.invoicePrefix = sanitizeInput(invoicePrefix.value) || 'INV';

        const autoBackup = document.getElementById('autoBackup');
        if (autoBackup) settings.autoBackup = autoBackup.checked;

        const lowStockAlerts = document.getElementById('lowStockAlerts');
        if (lowStockAlerts) settings.lowStockAlerts = lowStockAlerts.checked;

        const soundNotifications = document.getElementById('soundNotifications');
        if (soundNotifications) settings.soundNotifications = soundNotifications.checked;

        // إعدادات المظهر
        const theme = document.getElementById('theme');
        if (theme) settings.theme = theme.value;

        const fontSize = document.getElementById('fontSize');
        if (fontSize) settings.fontSize = fontSize.value;

        const primaryColor = document.getElementById('primaryColor');
        if (primaryColor) settings.primaryColor = primaryColor.value;

        const compactMode = document.getElementById('compactMode');
        if (compactMode) settings.compactMode = compactMode.checked;

        const showAnimations = document.getElementById('showAnimations');
        if (showAnimations) settings.showAnimations = showAnimations.checked;

        // حفظ الشعار إذا تم رفعه
        const logoPreview = document.querySelector('#logoPreview img');
        if (logoPreview) {
            settings.companyLogo = logoPreview.src;
        }

        // حفظ الإعدادات
        db.updateSettings(settings);
        showAlert('تم حفظ الإعدادات بنجاح', 'success');

        // تطبيق الإعدادات
        applySettings(settings);

    } catch (error) {
        console.error('خطأ في حفظ الإعدادات:', error);
        showAlert('حدث خطأ أثناء حفظ الإعدادات: ' + error.message, 'error');
    }
}

// تطبيق الإعدادات
function applySettings(settings) {
    try {
        if (settings.theme) {
            changeTheme(settings.theme);
        }

        if (settings.fontSize) {
            changeFontSize(settings.fontSize);
        }

        if (settings.primaryColor) {
            changePrimaryColor(settings.primaryColor);
        }

        if (settings.compactMode) {
            document.body.classList.add('compact-mode');
        } else {
            document.body.classList.remove('compact-mode');
        }

        if (settings.showAnimations === false) {
            document.body.classList.add('no-animations');
        } else {
            document.body.classList.remove('no-animations');
        }
    } catch (error) {
        console.error('خطأ في تطبيق الإعدادات:', error);
    }
}

// إعادة تعيين الإعدادات
function resetSettings() {
    showConfirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟', function() {
        try {
            db.resetSettings();
            loadSettings();
            applySettings(db.getSettings());
            showAlert('تم إعادة تعيين الإعدادات بنجاح', 'success');
        } catch (error) {
            showAlert('حدث خطأ أثناء إعادة تعيين الإعدادات', 'error');
        }
    });
}

// وظائف تغيير كلمة المرور
function togglePasswordVisibility(inputId) {
    const input = document.getElementById(inputId);
    const button = input.nextElementSibling;
    const icon = button.querySelector('i');

    if (input.type === 'password') {
        input.type = 'text';
        icon.className = 'fas fa-eye-slash';
    } else {
        input.type = 'password';
        icon.className = 'fas fa-eye';
    }
}

function checkPasswordStrength(password) {
    let strength = 0;
    const requirements = {
        length: password.length >= 6,
        number: /\d/.test(password),
        letter: /[a-zA-Z\u0600-\u06FF]/.test(password)
    };

    // تحديث متطلبات كلمة المرور
    document.getElementById('req-length').className = requirements.length ? 'valid' : '';
    document.getElementById('req-number').className = requirements.number ? 'valid' : '';
    document.getElementById('req-letter').className = requirements.letter ? 'valid' : '';

    // حساب قوة كلمة المرور
    if (requirements.length) strength += 1;
    if (requirements.number) strength += 1;
    if (requirements.letter) strength += 1;
    if (password.length >= 8) strength += 1;
    if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) strength += 1;

    const strengthBar = document.getElementById('strengthBar');
    const strengthText = document.getElementById('strengthText');

    strengthBar.className = 'strength-bar';

    if (strength <= 1) {
        strengthBar.classList.add('weak');
        strengthText.textContent = 'ضعيفة';
        strengthText.className = 'strength-text weak';
    } else if (strength <= 3) {
        strengthBar.classList.add('medium');
        strengthText.textContent = 'متوسطة';
        strengthText.className = 'strength-text medium';
    } else {
        strengthBar.classList.add('strong');
        strengthText.textContent = 'قوية';
        strengthText.className = 'strength-text strong';
    }

    strengthBar.style.width = (strength * 20) + '%';

    return Object.values(requirements).every(req => req);
}

function changePassword() {
    const currentPassword = document.getElementById('currentPassword').value;
    const newPassword = document.getElementById('newPassword').value;
    const confirmPassword = document.getElementById('confirmPassword').value;

    // التحقق من كلمة المرور الحالية
    if (!db.verifyPassword(currentPassword)) {
        showAlert('كلمة المرور الحالية غير صحيحة', 'error');
        return;
    }

    // التحقق من كلمة المرور الجديدة
    if (!newPassword) {
        showAlert('يرجى إدخال كلمة المرور الجديدة', 'error');
        return;
    }

    if (newPassword.length < 6) {
        showAlert('كلمة المرور يجب أن تكون 6 أحرف على الأقل', 'error');
        return;
    }

    // التحقق من تطابق كلمة المرور
    if (newPassword !== confirmPassword) {
        showAlert('كلمة المرور الجديدة وتأكيدها غير متطابقتين', 'error');
        return;
    }

    // التحقق من أن كلمة المرور الجديدة مختلفة عن الحالية
    if (currentPassword === newPassword) {
        showAlert('كلمة المرور الجديدة يجب أن تكون مختلفة عن الحالية', 'error');
        return;
    }

    try {
        // حفظ كلمة المرور الجديدة
        const settings = db.getSettings();
        settings.password = db.hashPassword(newPassword);
        db.saveSettings(settings);

        // مسح الحقول
        document.getElementById('passwordChangeForm').reset();

        showAlert('تم تغيير كلمة المرور بنجاح', 'success');

    } catch (error) {
        showAlert('حدث خطأ أثناء تغيير كلمة المرور', 'error');
    }
}

// إضافة مستمع لفحص قوة كلمة المرور
document.addEventListener('DOMContentLoaded', function() {
    // إضافة مستمع لحقل كلمة المرور الجديدة
    setTimeout(() => {
        const newPasswordInput = document.getElementById('newPassword');
        if (newPasswordInput) {
            newPasswordInput.addEventListener('input', function() {
                checkPasswordStrength(this.value);
            });
        }
    }, 1000);
});

// وظائف الأدوات المتقدمة
function deleteAllProducts() {
    showConfirm(
        'هل أنت متأكد من حذف جميع المنتجات؟ هذا الإجراء لا يمكن التراجع عنه.',
        function() {
            try {
                localStorage.removeItem('erp_products');
                showAlert('تم حذف جميع المنتجات بنجاح', 'success');
            } catch (error) {
                showAlert('حدث خطأ أثناء حذف المنتجات', 'error');
            }
        }
    );
}

function deleteAllExpenses() {
    showConfirm(
        'هل أنت متأكد من حذف جميع المصروفات؟ هذا الإجراء لا يمكن التراجع عنه.',
        function() {
            try {
                localStorage.removeItem('erp_expenses');
                showAlert('تم حذف جميع المصروفات بنجاح', 'success');
            } catch (error) {
                showAlert('حدث خطأ أثناء حذف المصروفات', 'error');
            }
        }
    );
}

function deleteAllCustomers() {
    showConfirm(
        'هل أنت متأكد من حذف جميع العملاء؟ هذا الإجراء لا يمكن التراجع عنه.',
        function() {
            try {
                localStorage.removeItem('erp_customers');
                showAlert('تم حذف جميع العملاء بنجاح', 'success');
            } catch (error) {
                showAlert('حدث خطأ أثناء حذف العملاء', 'error');
            }
        }
    );
}

function deleteAllSales() {
    showConfirm(
        'هل أنت متأكد من حذف جميع المبيعات؟ هذا الإجراء لا يمكن التراجع عنه.',
        function() {
            try {
                localStorage.removeItem('erp_sales');
                showAlert('تم حذف جميع المبيعات بنجاح', 'success');
            } catch (error) {
                showAlert('حدث خطأ أثناء حذف المبيعات', 'error');
            }
        }
    );
}

function resetAllSettings() {
    showConfirm(
        'هل أنت متأكد من إعادة تعيين جميع الإعدادات؟ سيتم فقدان جميع التخصيصات وإعادة الإعدادات للقيم الافتراضية.',
        function() {
            try {
                // إنشاء الإعدادات الافتراضية
                const defaultSettings = {
                    companyName: 'شركة نقاط البيع',
                    companyAddress: 'الرياض، المملكة العربية السعودية',
                    companyPhone: '٠١١-٤٥٦-٧٨٩٠',
                    companyEmail: '<EMAIL>',
                    companyWebsite: 'www.company.com',
                    taxNumber: '١٢٣٤٥٦٧٨٩٠',
                    commercialRegister: '١٠٢٠٣٠٤٠٥٠',
                    taxRate: 15,
                    currency: 'ريال',
                    password: db.hashPassword('123'),
                    theme: 'light',
                    language: 'ar',
                    lowStockAlert: 10,
                    lowStockThreshold: 10,
                    autoBackup: true,
                    lowStockAlerts: true,
                    soundNotifications: true,
                    invoicePrefix: 'INV',
                    fontSize: 'medium',
                    primaryColor: '#667eea',
                    compactMode: false,
                    showAnimations: true,
                    printSettings: {
                        showLogo: true,
                        showHeader: true,
                        showFooter: true,
                        paperSize: 'A4',
                        orientation: 'portrait'
                    }
                };

                // حفظ الإعدادات الافتراضية
                localStorage.setItem('erp_settings', JSON.stringify(defaultSettings));

                showAlert('تم إعادة تعيين جميع الإعدادات للقيم الافتراضية بنجاح. سيتم إعادة تحميل الصفحة.', 'success');
                setTimeout(() => {
                    location.reload();
                }, 2000);
            } catch (error) {
                showAlert('حدث خطأ أثناء إعادة تعيين الإعدادات', 'error');
            }
        }
    );
}

function clearAllSystemData() {
    showConfirm(
        'تحذير: هذا الإجراء سيحذف جميع البيانات نهائياً (المنتجات، العملاء، المبيعات، المصروفات، الإعدادات). هل أنت متأكد؟',
        function() {
            showConfirm(
                'هذا تأكيد نهائي. سيتم حذف جميع بيانات النظام ولا يمكن استعادتها. هل تريد المتابعة؟',
                function() {
                    try {
                        db.clearAllData();
                        showAlert('تم مسح جميع بيانات النظام. سيتم إعادة تحميل الصفحة.', 'success');
                        setTimeout(() => {
                            location.reload();
                        }, 2000);
                    } catch (error) {
                        showAlert('حدث خطأ أثناء مسح البيانات', 'error');
                    }
                }
            );
        }
    );
}

// وظائف تغيير كلمة المرور
function togglePasswordVisibility(inputId) {
    const input = document.getElementById(inputId);
    const button = input.nextElementSibling;
    const icon = button.querySelector('i');

    if (input.type === 'password') {
        input.type = 'text';
        icon.className = 'fas fa-eye-slash';
    } else {
        input.type = 'password';
        icon.className = 'fas fa-eye';
    }
}

function checkPasswordStrength(password) {
    let strength = 0;
    const requirements = {
        length: password.length >= 6,
        number: /\d/.test(password),
        letter: /[a-zA-Z\u0600-\u06FF]/.test(password)
    };

    // تحديث متطلبات كلمة المرور
    document.getElementById('req-length').className = requirements.length ? 'valid' : '';
    document.getElementById('req-number').className = requirements.number ? 'valid' : '';
    document.getElementById('req-letter').className = requirements.letter ? 'valid' : '';

    // حساب قوة كلمة المرور
    if (requirements.length) strength += 1;
    if (requirements.number) strength += 1;
    if (requirements.letter) strength += 1;
    if (password.length >= 8) strength += 1;
    if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) strength += 1;

    const strengthBar = document.getElementById('strengthBar');
    const strengthText = document.getElementById('strengthText');

    strengthBar.className = 'strength-bar';

    if (strength <= 1) {
        strengthBar.classList.add('weak');
        strengthText.textContent = 'ضعيفة';
        strengthText.className = 'strength-text weak';
    } else if (strength <= 3) {
        strengthBar.classList.add('medium');
        strengthText.textContent = 'متوسطة';
        strengthText.className = 'strength-text medium';
    } else {
        strengthBar.classList.add('strong');
        strengthText.textContent = 'قوية';
        strengthText.className = 'strength-text strong';
    }

    strengthBar.style.width = (strength * 20) + '%';

    return Object.values(requirements).every(req => req);
}

function changePassword() {
    const currentPassword = document.getElementById('currentPassword').value;
    const newPassword = document.getElementById('newPassword').value;
    const confirmPassword = document.getElementById('confirmPassword').value;

    // التحقق من كلمة المرور الحالية
    if (!db.verifyPassword(currentPassword)) {
        showAlert('كلمة المرور الحالية غير صحيحة', 'error');
        return;
    }

    // التحقق من كلمة المرور الجديدة
    if (!newPassword) {
        showAlert('يرجى إدخال كلمة المرور الجديدة', 'error');
        return;
    }

    if (newPassword.length < 6) {
        showAlert('كلمة المرور يجب أن تكون 6 أحرف على الأقل', 'error');
        return;
    }

    // التحقق من تطابق كلمة المرور
    if (newPassword !== confirmPassword) {
        showAlert('كلمة المرور الجديدة وتأكيدها غير متطابقتين', 'error');
        return;
    }

    // التحقق من أن كلمة المرور الجديدة مختلفة عن الحالية
    if (currentPassword === newPassword) {
        showAlert('كلمة المرور الجديدة يجب أن تكون مختلفة عن الحالية', 'error');
        return;
    }

    try {
        // حفظ كلمة المرور الجديدة
        const settings = db.getSettings();
        settings.password = db.hashPassword(newPassword);
        db.saveSettings(settings);

        // مسح الحقول
        document.getElementById('passwordChangeForm').reset();

        showAlert('تم تغيير كلمة المرور بنجاح', 'success');

    } catch (error) {
        showAlert('حدث خطأ أثناء تغيير كلمة المرور', 'error');
    }
}

// إضافة مستمع لفحص قوة كلمة المرور
document.addEventListener('DOMContentLoaded', function() {
    // إضافة مستمع لحقل كلمة المرور الجديدة
    setTimeout(() => {
        const newPasswordInput = document.getElementById('newPassword');
        if (newPasswordInput) {
            newPasswordInput.addEventListener('input', function() {
                checkPasswordStrength(this.value);
            });
        }
    }, 1000);
});

// إنشاء نسخة احتياطية (للأدوات المتقدمة)
function exportBackup() {
    try {
        const backupData = db.exportData();
        const dataBlob = new Blob([backupData], { type: 'application/json' });

        const link = document.createElement('a');
        link.href = URL.createObjectURL(dataBlob);
        link.download = `backup-${new Date().toISOString().split('T')[0]}.json`;
        link.click();

        showAlert('تم إنشاء النسخة الاحتياطية بنجاح', 'success');

    } catch (error) {
        showAlert('حدث خطأ أثناء إنشاء النسخة الاحتياطية', 'error');
    }
}

// استعادة نسخة احتياطية (للأدوات المتقدمة)
function importBackup(input) {
    const file = input.files[0];
    if (!file) return;

    showConfirm('هل أنت متأكد من استعادة البيانات؟ سيتم استبدال جميع البيانات الحالية.', function() {
        const reader = new FileReader();
        reader.onload = function(e) {
            try {
                db.importData(e.target.result);
                showAlert('تم استعادة البيانات بنجاح. سيتم إعادة تحميل الصفحة.', 'success');
                setTimeout(() => {
                    location.reload();
                }, 2000);

            } catch (error) {
                showAlert('حدث خطأ أثناء استعادة البيانات. تأكد من صحة ملف النسخة الاحتياطية.', 'error');
            }
        };
        reader.readAsText(file);
    });

    // إعادة تعيين قيمة input
    input.value = '';
}
