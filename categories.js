// إدارة الأصناف

// HTML صفحة الأصناف
function getCategoriesHTML() {
    return `
        <div class="page-header">
            <div class="page-title">
                <h2>إدارة الأصناف</h2>
            </div>
            <div class="page-actions">
                <button class="btn btn-primary" onclick="showAddCategoryModal()">
                    <i class="fas fa-plus"></i>
                    إضافة صنف جديد
                </button>
            </div>
        </div>

        <!-- إحصائيات الأصناف -->
        <div class="stats-grid">
            <div class="stat-card primary">
                <div class="stat-icon">
                    <i class="fas fa-tags"></i>
                </div>
                <div class="stat-content">
                    <h3 id="totalCategories">0</h3>
                    <p>إجمالي الأصناف</p>
                </div>
            </div>
            <div class="stat-card success">
                <div class="stat-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="stat-content">
                    <h3 id="activeCategories">0</h3>
                    <p>الأصناف النشطة</p>
                </div>
            </div>
            <div class="stat-card warning">
                <div class="stat-icon">
                    <i class="fas fa-boxes"></i>
                </div>
                <div class="stat-content">
                    <h3 id="categoriesWithProducts">0</h3>
                    <p>أصناف بها منتجات</p>
                </div>
            </div>
            <div class="stat-card info">
                <div class="stat-icon">
                    <i class="fas fa-eye-slash"></i>
                </div>
                <div class="stat-content">
                    <h3 id="inactiveCategories">0</h3>
                    <p>الأصناف غير النشطة</p>
                </div>
            </div>
        </div>

        <!-- شريط البحث والفلترة -->
        <div class="search-filter-bar">
            <div class="search-group">
                <div class="search-input">
                    <input type="text" id="categorySearch" placeholder="البحث في الأصناف...">
                    <i class="fas fa-search"></i>
                </div>
            </div>
            <div class="filter-group">
                <select id="categoryStatusFilter">
                    <option value="">جميع الحالات</option>
                    <option value="active">نشط</option>
                    <option value="inactive">غير نشط</option>
                </select>
            </div>
        </div>

        <!-- جدول الأصناف -->
        <div class="table-container">
            <table class="data-table">
                <thead>
                    <tr>
                        <th>اسم الصنف</th>
                        <th>الوصف</th>
                        <th>عدد المنتجات</th>
                        <th>الحالة</th>
                        <th>تاريخ الإنشاء</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody id="categoriesTableBody">
                    <!-- سيتم ملء البيانات هنا -->
                </tbody>
            </table>
        </div>

        <!-- نافذة إضافة/تعديل صنف -->
        <div id="categoryModal" class="modal hidden">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 id="categoryModalTitle">إضافة صنف جديد</h3>
                </div>
                <div class="modal-body">
                    <form id="categoryForm">
                        <div class="form-group">
                            <label for="categoryName">اسم الصنف *</label>
                            <input type="text" id="categoryName" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="categoryDescription">الوصف</label>
                            <textarea id="categoryDescription" rows="3" placeholder="وصف الصنف (اختياري)"></textarea>
                        </div>
                        
                        <div class="form-group">
                            <label for="categoryColor">لون الصنف</label>
                            <input type="color" id="categoryColor" value="#6366f1">
                        </div>
                        
                        <div class="form-group">
                            <label for="categoryIcon">أيقونة الصنف</label>
                            <select id="categoryIcon">
                                <option value="fas fa-tag">علامة</option>
                                <option value="fas fa-laptop">إلكترونيات</option>
                                <option value="fas fa-tshirt">ملابس</option>
                                <option value="fas fa-utensils">طعام</option>
                                <option value="fas fa-book">كتب</option>
                                <option value="fas fa-gamepad">ألعاب</option>
                                <option value="fas fa-home">منزل</option>
                                <option value="fas fa-car">سيارات</option>
                                <option value="fas fa-medkit">طبي</option>
                                <option value="fas fa-tools">أدوات</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="categoryActive" checked>
                                <span class="checkmark"></span>
                                صنف نشط
                            </label>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" onclick="hideCategoryModal()">إلغاء</button>
                    <button class="btn btn-primary" onclick="saveCategory()">حفظ</button>
                </div>
            </div>
        </div>

        <!-- نافذة تفاصيل الصنف -->
        <div id="categoryDetailsModal" class="modal hidden">
            <div class="modal-content large-modal">
                <div class="modal-header">
                    <h3>تفاصيل الصنف</h3>
                </div>
                <div class="modal-body">
                    <div id="categoryDetailsContent">
                        <!-- سيتم ملء التفاصيل هنا -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" onclick="hideCategoryDetailsModal()">إغلاق</button>
                    <button class="btn btn-primary" onclick="printCategoryDetails()">طباعة</button>
                </div>
            </div>
        </div>
    `;
}

// متغيرات الأصناف
let categoriesData = [];
let currentCategory = null;

// تهيئة صفحة الأصناف
function initializeCategories() {
    loadCategories();
    updateCategoriesStats();
    setupCategoriesEventListeners();
}

// إعداد مستمعي الأحداث
function setupCategoriesEventListeners() {
    // البحث
    document.getElementById('categorySearch').addEventListener('input', function() {
        filterCategories();
    });
    
    // فلترة الحالة
    document.getElementById('categoryStatusFilter').addEventListener('change', function() {
        filterCategories();
    });
}

// تحميل الأصناف
function loadCategories() {
    categoriesData = db.getData('categories') || [];
    displayCategories(categoriesData);
}

// عرض الأصناف في الجدول
function displayCategories(categories) {
    const tbody = document.getElementById('categoriesTableBody');
    
    if (categories.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="6" class="text-center">لا توجد أصناف</td>
            </tr>
        `;
        return;
    }
    
    tbody.innerHTML = categories.map(category => {
        const productsCount = getProductsCountByCategory(category.id);
        const statusBadge = category.active ? 
            '<span class="badge badge-success">نشط</span>' : 
            '<span class="badge badge-danger">غير نشط</span>';
            
        return `
            <tr>
                <td>
                    <div class="category-info">
                        <i class="${category.icon}" style="color: ${category.color}; margin-left: 8px;"></i>
                        <strong>${category.name}</strong>
                    </div>
                </td>
                <td>${category.description || '-'}</td>
                <td>${productsCount}</td>
                <td>${statusBadge}</td>
                <td>${db.formatDate(category.createdAt)}</td>
                <td>
                    <div class="action-buttons">
                        <button class="btn btn-sm btn-info" onclick="showCategoryDetails('${category.id}')" title="التفاصيل">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-warning" onclick="editCategory('${category.id}')" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="deleteCategory('${category.id}')" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }).join('');
}

// تحديث إحصائيات الأصناف
function updateCategoriesStats() {
    const total = categoriesData.length;
    const active = categoriesData.filter(c => c.active).length;
    const inactive = total - active;
    const withProducts = categoriesData.filter(c => getProductsCountByCategory(c.id) > 0).length;
    
    document.getElementById('totalCategories').textContent = total;
    document.getElementById('activeCategories').textContent = active;
    document.getElementById('inactiveCategories').textContent = inactive;
    document.getElementById('categoriesWithProducts').textContent = withProducts;
}

// الحصول على عدد المنتجات في صنف معين
function getProductsCountByCategory(categoryId) {
    const products = db.getData('products') || [];
    return products.filter(p => p.categoryId === categoryId).length;
}

// فلترة الأصناف
function filterCategories() {
    const searchTerm = document.getElementById('categorySearch').value.toLowerCase();
    const statusFilter = document.getElementById('categoryStatusFilter').value;
    
    let filteredCategories = categoriesData;
    
    // فلترة البحث
    if (searchTerm) {
        filteredCategories = filteredCategories.filter(category =>
            category.name.toLowerCase().includes(searchTerm) ||
            (category.description && category.description.toLowerCase().includes(searchTerm))
        );
    }
    
    // فلترة الحالة
    if (statusFilter) {
        filteredCategories = filteredCategories.filter(category => {
            if (statusFilter === 'active') return category.active;
            if (statusFilter === 'inactive') return !category.active;
            return true;
        });
    }
    
    displayCategories(filteredCategories);
}

// عرض نافذة إضافة صنف
function showAddCategoryModal() {
    currentCategory = null;
    document.getElementById('categoryModalTitle').textContent = 'إضافة صنف جديد';
    document.getElementById('categoryForm').reset();
    document.getElementById('categoryColor').value = '#6366f1';
    document.getElementById('categoryActive').checked = true;
    document.getElementById('categoryModal').classList.remove('hidden');
}

// إخفاء نافذة الصنف
function hideCategoryModal() {
    document.getElementById('categoryModal').classList.add('hidden');
    currentCategory = null;
}

// تعديل صنف
function editCategory(categoryId) {
    const category = categoriesData.find(c => c.id === categoryId);
    if (!category) return;

    currentCategory = category;
    document.getElementById('categoryModalTitle').textContent = 'تعديل الصنف';
    document.getElementById('categoryName').value = category.name;
    document.getElementById('categoryDescription').value = category.description || '';
    document.getElementById('categoryColor').value = category.color || '#6366f1';
    document.getElementById('categoryIcon').value = category.icon || 'fas fa-tag';
    document.getElementById('categoryActive').checked = category.active;

    document.getElementById('categoryModal').classList.remove('hidden');
}

// حفظ الصنف
function saveCategory() {
    const name = document.getElementById('categoryName').value.trim();
    const description = document.getElementById('categoryDescription').value.trim();
    const color = document.getElementById('categoryColor').value;
    const icon = document.getElementById('categoryIcon').value;
    const active = document.getElementById('categoryActive').checked;

    if (!name) {
        showAlert('يرجى إدخال اسم الصنف', 'error');
        return;
    }

    // التحقق من عدم تكرار الاسم
    const existingCategory = categoriesData.find(c =>
        c.name.toLowerCase() === name.toLowerCase() &&
        (!currentCategory || c.id !== currentCategory.id)
    );

    if (existingCategory) {
        showAlert('اسم الصنف موجود بالفعل', 'error');
        return;
    }

    const categoryData = {
        name,
        description,
        color,
        icon,
        active
    };

    try {
        if (currentCategory) {
            // تحديث صنف موجود
            db.updateItem('categories', currentCategory.id, categoryData);
            showAlert('تم تحديث الصنف بنجاح', 'success');
        } else {
            // إضافة صنف جديد
            db.addItem('categories', categoryData);
            showAlert('تم إضافة الصنف بنجاح', 'success');
        }

        hideCategoryModal();
        loadCategories();
        updateCategoriesStats();

    } catch (error) {
        showAlert('حدث خطأ أثناء حفظ الصنف', 'error');
    }
}

// حذف صنف
function deleteCategory(categoryId) {
    const category = categoriesData.find(c => c.id === categoryId);
    if (!category) return;

    // التحقق من وجود منتجات في هذا الصنف
    const productsCount = getProductsCountByCategory(categoryId);
    if (productsCount > 0) {
        showAlert(`لا يمكن حذف الصنف لأنه يحتوي على ${productsCount} منتج`, 'error');
        return;
    }

    showConfirm(`هل أنت متأكد من حذف الصنف "${category.name}"؟`, function() {
        try {
            db.deleteItem('categories', categoryId);
            showAlert('تم حذف الصنف بنجاح', 'success');
            loadCategories();
            updateCategoriesStats();
        } catch (error) {
            showAlert('حدث خطأ أثناء حذف الصنف', 'error');
        }
    });
}

// عرض تفاصيل الصنف
function showCategoryDetails(categoryId) {
    const category = categoriesData.find(c => c.id === categoryId);
    if (!category) return;

    const products = db.getData('products').filter(p => p.categoryId === categoryId);
    const productsCount = products.length;
    const totalValue = products.reduce((sum, p) => sum + (p.price * p.quantity), 0);

    const detailsHTML = `
        <div class="category-details">
            <div class="category-header">
                <div class="category-icon" style="background: ${category.color}">
                    <i class="${category.icon}"></i>
                </div>
                <div class="category-info">
                    <h3>${category.name}</h3>
                    <p>${category.description || 'لا يوجد وصف'}</p>
                </div>
            </div>

            <div class="details-grid">
                <div class="detail-row">
                    <span class="detail-label">الحالة:</span>
                    <span class="detail-value">
                        ${category.active ?
                            '<span class="badge badge-success">نشط</span>' :
                            '<span class="badge badge-danger">غير نشط</span>'
                        }
                    </span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">عدد المنتجات:</span>
                    <span class="detail-value">${productsCount}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">إجمالي قيمة المنتجات:</span>
                    <span class="detail-value">${db.formatCurrency(totalValue)}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">تاريخ الإنشاء:</span>
                    <span class="detail-value">${db.formatDateTime(category.createdAt)}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">آخر تحديث:</span>
                    <span class="detail-value">${db.formatDateTime(category.updatedAt)}</span>
                </div>
            </div>

            ${productsCount > 0 ? `
                <div class="products-section">
                    <h4>المنتجات في هذا الصنف</h4>
                    <div class="products-list">
                        ${products.map(product => `
                            <div class="product-item">
                                <div class="product-info">
                                    <strong>${product.name}</strong>
                                    <span class="product-price">${db.formatCurrency(product.price)}</span>
                                </div>
                                <div class="product-quantity">الكمية: ${product.quantity}</div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            ` : ''}
        </div>
    `;

    document.getElementById('categoryDetailsContent').innerHTML = detailsHTML;
    document.getElementById('categoryDetailsModal').classList.remove('hidden');
}

// إخفاء نافذة تفاصيل الصنف
function hideCategoryDetailsModal() {
    document.getElementById('categoryDetailsModal').classList.add('hidden');
}

// طباعة تفاصيل الصنف
function printCategoryDetails() {
    const content = document.getElementById('categoryDetailsContent').innerHTML;
    printContent(content, 'تفاصيل الصنف');
}

// تهيئة بيانات الأصناف الافتراضية
function initializeDefaultCategories() {
    const existingCategories = db.getData('categories');
    if (!existingCategories || existingCategories.length === 0) {
        const defaultCategories = [
            {
                name: 'عام',
                description: 'منتجات عامة',
                color: '#6366f1',
                icon: 'fas fa-tag',
                active: true
            },
            {
                name: 'إلكترونيات',
                description: 'أجهزة إلكترونية ومعدات تقنية',
                color: '#3b82f6',
                icon: 'fas fa-laptop',
                active: true
            },
            {
                name: 'ملابس',
                description: 'ملابس وأزياء',
                color: '#ec4899',
                icon: 'fas fa-tshirt',
                active: true
            }
        ];

        defaultCategories.forEach(category => {
            db.addItem('categories', category);
        });
    }
}
